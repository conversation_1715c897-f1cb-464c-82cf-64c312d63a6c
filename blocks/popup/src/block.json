{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "bl/popup", "version": "0.1.0", "title": "Popup", "category": "widgets", "icon": "smiley", "description": "A popup block containing text, a button, a close icon, and a background image.\"", "example": {}, "supports": {"html": true, "align": true, "alignWide": true, "customClassName": true, "color": {"text": true, "background": true, "enableContrastChecker": true, "heading": true}, "spacing": {"margin": true, "padding": true, "blockGap": true}, "typography": {"fontSize": true, "lineHeight": true, "textAlign": true}}, "attributes": {"text": {"type": "string", "default": "Your popup text here"}, "buttonText": {"type": "string", "default": "Click Me!"}, "backgroundImage": {"type": "string", "default": ""}, "innerImage": {"type": "string", "default": ""}, "closeIcon": {"type": "string", "default": ""}, "enabled": {"type": "boolean", "default": true}, "altPopupClass": {"type": "string", "default": "wp-block-bl-popup"}}, "textdomain": "popup", "editorScript": "file:./index.js", "editorStyle": "file:./index.css", "style": "file:./style-index.css", "viewScript": "file:./view.js"}