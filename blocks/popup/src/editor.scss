/**
 * The following styles get applied inside the editor only.
 *
 * Replace them with your own styles or remove the file completely.
 */
.wp-block-bl-popup {
	background-size: 100% 60%;
	background-repeat: no-repeat;
	width: 580px;
	padding: 20px;
	@media (max-width: 1024px) {
		width:80%!important;
	}
	@media (max-width: 1440px) {
		background-size: 100% 61%;
	}
	@media (max-width: 768px) {
		background-size: 100% 58%;
	}
	@media (max-width: 480px) {
		background-size: 100% 55%;

	}
	.popup-preview{
		background-size: cover;
		background-position: center;
		max-width: 100%;
		display: block;
		//flex-direction: column;
		.popup-inner-image{
			height: auto;
			max-width:362px;
			margin-bottom: 20px;
			align-self: center;
			@media (max-width: 768px) {
				max-width:300px;
			}
		}
		p{
			margin: 0 0 20px;
		}
		.popup-close-icon {
			position: absolute;
			top: 5px;
			right: 5px;
			background: transparent;
			border: none;
			width: 18px;
			height: 18px;
			padding: 20px;
			cursor: pointer;
		}

	}
}
.wp-block-bl-popup.minimized {
	top: unset;
	height: 190px;
	width:80%!important;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	.popup-frontend{
		display: flex;
		flex-direction: row;
		height: 100%;
		max-width: 100%;
		justify-content: flex-end;
		@media (max-width: 768px) {
			justify-content: center;
		}
		.popup-inner-image{
			bottom: -94px;
			height: auto;
			left: 20px;
			margin-bottom: 0;
			width: 100%;
			position: absolute;
			@media (max-width: 768px) {
				display: none;
			}
		}
		.promotion-popup-inner{
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 65%;
			flex-shrink: 1;
			z-index: 9999;
		}
	}
}
.wp-block-bl-popup.minimized .popup-button,
.wp-block-bl-popup.minimized .popup-text {
	display: none;
}
