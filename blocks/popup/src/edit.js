import {__} from '@wordpress/i18n';
import {
	InspectorControls,
	MediaUpload,
	useBlockProps,
	InnerBlocks,
} from '@wordpress/block-editor';
import {
	PanelBody,
	PanelRow,
	TextControl,
	Button,
	ToggleControl,
} from '@wordpress/components';

// Import editor styles
import './editor.scss';

export default function Edit({attributes, setAttributes}) {
	const {backgroundImage, enabled, closeIcon,innerImage, altPopupClass} = attributes;

	// Handler for setting the background image
	const onSelectBackgroundImage = (media) => {
		setAttributes({backgroundImage: media.url});
	};
	const onSelectCloseIcon = (media) => {
		setAttributes({closeIcon: media.url});
	};
	const onSelectInnerImage = (media) => {
		setAttributes({innerImage: media.url});
	}
	
	// Save block props for rendering in the editor
	const blockProps = useBlockProps();

	// InnerBlocks template - Predefines a paragraph and button block
	const TEMPLATE = [
		['core/paragraph', {placeholder: __('Enter your text...', 'popup')}],
		['core/buttons', {text: __('Click Me', 'popup')}],
	];

	return (
		<>
				<InspectorControls>
					<PanelBody title={__('Popup Settings', 'popup')} initialOpen={true}>
						<PanelRow>
							<MediaUpload
								onSelect={onSelectBackgroundImage}
								allowedTypes={['image']}
								render={({open}) => (
									<Button onClick={open} variant="primary">
										{__('Select Background Image', 'popup')}
									</Button>
								)}
							/>
						</PanelRow>
						<PanelRow>
							<MediaUpload
								onSelect={onSelectInnerImage}
								allowedTypes={['image']}
								render={({open}) => (
									<Button onClick={open} variant="primary">
										{__('Select Inner Image', 'popup')}
									</Button>
								)}
							/>
						</PanelRow>
						<PanelRow>
							<MediaUpload
								onSelect={onSelectCloseIcon}
								allowedTypes={['image']}
								render={({open}) => (
									<Button onClick={open} variant="primary">
										{__('Select Close Icon', 'popup')}
									</Button>
								)}
							/>
						</PanelRow>
							<PanelRow>
								<ToggleControl
									label={__('Enable Popup', 'popup')}
									checked={enabled}
									onChange={(value) => setAttributes({enabled: value})}
								/>
							</PanelRow>
							<PanelRow>
								<TextControl
									label={__('Pinned Popup Class', 'popup')}
									value={altPopupClass}
									onChange={(value) => setAttributes({altPopupClass: value})}
								/>
							</PanelRow>
					
					</PanelBody>
				</InspectorControls>

				<div
					className="bl-popup"
					{...blockProps}
				>
					<div
						className="popup-preview"
						style={{
							backgroundImage: backgroundImage
								? `url(${backgroundImage})`
								: undefined,
						}}
					>
						<img className="popup-inner-image" src={innerImage} alt="inner-image"/>
						<div className="promotion-popup-inner">
							<InnerBlocks
								template={TEMPLATE}
								allowedBlocks={['core/paragraph', 'core/buttons']}
							/>
							<img className="popup-close-icon" src={closeIcon} alt="close-icon"/>
						</div>
					</div>
				</div>
		</>
	);
}
