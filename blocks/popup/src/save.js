import {useBlockProps, InnerBlocks} from '@wordpress/block-editor';

export default function save({attributes}) {
	const {backgroundImage, closeIcon, innerImage, altPopupClass} = attributes;

	const blockProps = useBlockProps.save(
		{'data-alt-popup-class':altPopupClass}
	);

	return (
		<div
			className="bl-popup"
			{...blockProps}
			style={{
				backgroundImage: backgroundImage
					? `url(${backgroundImage})`
					: undefined,
			}}
		>
			<div className="popup-frontend">
				<img className="popup-inner-image" src={innerImage} alt="inner-image"/>
				<div className="promotion-popup-inner">
					<InnerBlocks.Content/>
					<img className="popup-close-icon" id="popup-close" src={closeIcon} alt="close-icon"/>
				</div>
			</div>
		</div>
	);
}
