/**
 * Use this file for JavaScript code that you want to run in the front-end
 * on posts/pages that contain this block.
 *
 * When this file is defined as the value of the `viewScript` property
 * in `block.json` it will be enqueued on the front end of the site.
 *
 * Example:
 *
 * ```js
 * {
 *   "viewScript": "file:./view.js"
 * }
 * ```
 *
 * If you're not making any changes to this file because your project doesn't need any
 * JavaScript running in the front-end, then you should delete this file and remove
 * the `viewScript` property from `block.json`.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-metadata/#view-script
 */

/* eslint-disable no-console */
document.addEventListener('DOMContentLoaded', () => {
	let popup = document.querySelector('.wp-block-bl-popup');
	let popupToHide = '';
	if (!popup) return;

	const altPopupClass = popup.dataset.altPopupClass;
	console.log(popup)
	console.log(altPopupClass)

	// Set a timeout to show the popup after 5 seconds
	setTimeout(() => {
		popup.style.display = 'block'; // Display the popup
		if (altPopupClass !== '') {
		
			popupToHide = popup;
		
			popup = document.querySelector(`.${altPopupClass}`);
			console.log('Alt popup class', altPopupClass);
			console.log('popupToHide', popupToHide);
			console.log('popup', popup);
	
		}
	}, 3000); // 5000ms = 5 seconds



	const closeButton = document.querySelector('.wp-block-bl-popup #popup-close');
	let isSticky = false;

	// Close button click handler
	closeButton.addEventListener('click', () => {
		if (popupToHide !== '') {
			popupToHide.style.display = 'none';
		}
		if (popup.classList.contains('minimized')) {
			// If already minimized, hide the popup
			popup.style.display = 'none';
		} else {
			// Minimize popup
			popup.classList.add('minimized');
		}

	});

	// Scroll event handler
	window.addEventListener('scroll', () => {
		const scrollableHeight = document.documentElement.scrollHeight - window.innerHeight;
		const currentScroll = window.scrollY;

		// Hide popup if scrolled to bottom and minimized
		if (popup.classList.contains('minimized') && currentScroll >= scrollableHeight) {
			popup.style.display = 'none';
		}
	});
});/* eslint-enable no-console */
