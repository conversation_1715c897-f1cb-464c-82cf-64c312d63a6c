/**
 * React hook that is used to mark the block wrapper element.
 * It provides all the necessary props like the class name.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops
 */
import {useBlockProps} from '@wordpress/block-editor';

/**
 * The save function defines the way in which the different attributes should
 * be combined into the final markup, which is then serialized by the block
 * editor into `post_content`.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-edit-save/#save
 *
 * @return {Element} Element to render.
 */

export default function save({attributes}) {
	const blockProps = useBlockProps.save();
	const {languages} = attributes;
	const svgArrow = (
		<svg id="desktop-arrow-dropdown" viewBox="0 0 8 5">
			<path
				d="M1.41421 0C0.523309 0 0.077142 1.07714 0.707107 1.70711L3.29289 4.29289C3.68342 4.68342 4.31658 4.68342 4.70711 4.29289L7.2929 1.70711C7.92286 1.07714 7.47669 0 6.58579 0H1.41421Z"/>
		</svg>
	)
	return (
		<>
			<div {...blockProps}>
				<div className="language-switcher">
					<div className="current-language">
						<>
							{Object.values(languages).map(value => (
								value.current_language ? (
									<a href={value.url} className="lang-list__link" key={value.slug}>
										{attributes.isFlag && <img src={value.flag} alt={value.name}/>}
										{attributes.isName && value.name}
										{svgArrow}
									</a>
								) : null
							))}
						</>
					</div>
					<div className="lang-list">
						<>
							{Object.values(languages).map(value => (
								attributes.isHideCurrentLang && value.current_language ? null :
									<div className="lang-list__item" key={value.slug}>
										<a href={value.url} className="lang-list__link">
											{attributes.isFlag && <img src={value.flag} alt={value.name}/>}
											{attributes.isName && value.name}
										</a>
									</div>
							))}
						</>
					</div>
				</div>
			</div>
		</>
	);
}
