<?php
/**
 * @see https://github.com/WordPress/gutenberg/blob/trunk/docs/reference-guides/block-api/block-metadata.md#render
 */
$languages = pll_the_languages(
	array(
		'dropdown' => $attributes['isDropdown'] === true ? 1 : 0,
		'show_names' => $attributes['isName'] === true ? 1 : 0,
		'show_flags' => $attributes['isFlag'] === true ? 1 : 0,
		'force_home' => $attributes['isForceFrontpage'] === true ? 1 : 0,
		'hide_current' => $attributes['isHideCurrentLang'] === 0,
		'hide_if_empty' => $attributes['isHideLangWithoutTranslation'] === true ? 1 : 0,
		'raw' => 1,
	)
);
?>

<div <?php echo get_block_wrapper_attributes(); ?>>
	<?php if ($languages) : ?>
		<div class="language-switcher">
			<div class="current-language">
				<?php
				$current_language_slug = pll_current_language('slug');
				$current_language = $languages[$current_language_slug];
				?>
				<a href="<?php echo $current_language['url'] ?>" class="lang-list__link">
					<?php echo $current_language['flag']; ?>
					<?php if ($attributes['isName']) : ?>
						<span class="lang-list__name">
						<?php echo esc_html($current_language['name']); ?>
					</span>
					<?php endif; ?>
					<svg id="desktop-arrow-dropdown" viewBox="0 0 8 5">
						<path
							d="M1.41421 0C0.523309 0 0.077142 1.07714 0.707107 1.70711L3.29289 4.29289C3.68342 4.68342 4.31658 4.68342 4.70711 4.29289L7.2929 1.70711C7.92286 1.07714 7.47669 0 6.58579 0H1.41421Z"/>
					</svg>
				</a>
			</div>
			<div class="lang-list">
				<?php foreach ($languages as $language) : ?>
					<?php if ($attributes['isHideCurrentLang'] && $language['slug'] === $current_language_slug) : ?>
						<div class="lang-list__item">
							<a href="<?php echo esc_url($language['url']); ?>" class="lang-list__link">
								<?php if ($attributes['isFlag']) : ?>
									<?php echo $language['flag']; ?>
								<?php endif; ?>
								<?php if ($attributes['isName']) : ?>
									<span class="lang-list__name">
								<?php echo esc_html($language['name']); ?>
							</span>
								<?php endif; ?>
							</a>
						</div>
					<?php endif; ?>
				<?php endforeach; ?>
			</div>
		</div>
	<?php endif; ?>
</div>

