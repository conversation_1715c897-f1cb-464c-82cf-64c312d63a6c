/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 *
 * Replace them with your own styles or remove the file completely.
 */

.wp-block-bl-polylang-switcher {
	.language-switcher {
		min-width: 60px;
		position: relative;
		cursor: pointer;

		&:hover .lang-list {
			visibility: visible;
			opacity: 1;
		}
		&:hover svg{
			rotate: 180deg;
		}
		.lang-list{
			&__name {
				white-space: nowrap;
				font-size: 16px;
				margin-inline-end: 5px;
			}
		}

		.current-language {
			a {
				display: flex;
				flex-shrink: 0;
				align-items: center;
				color: #fff;
				padding: 10px 10px;

				img {
					margin-inline-end: 12px;
				}

				svg {
					fill: #fff;
					width: 8px;
					height: 5px;
				}
			}
		}

		.lang-list {
			visibility: hidden;
			opacity: 0;
			transition: visibility 0s, opacity 0.5s linear;
			position: absolute;
			top: 100%;
			background: rgba(0, 0, 0, .3);

			&__item a {
				padding: 10px 10px;
				display: flex;
				align-items: center;
				justify-content: normal;
				color: #fff;

				img {
					margin-inline-end: 12px;
				}

				&:hover {
					background: rgba(0, 0, 0, .6);
				}
			}

			&__name {
				white-space: nowrap;
				font-size: 16px;
			}
		}

		img {
			width: 28px;
			height: 20px;
		}

	}
}
