{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "bl/polylang-switcher", "version": "0.1.0", "title": "<PERSON><PERSON>ng Switcher", "category": "widgets", "icon": "translation", "description": "Language switcher for Polylang", "example": {}, "supports": {"html": false}, "textdomain": "polylang-switcher", "editorScript": "file:./index.js", "editorStyle": "file:./index.css", "style": "file:./style-index.css", "attributes": {"isDropdown": {"type": "boolean", "default": true}, "isName": {"type": "boolean", "default": false}, "isFlag": {"type": "boolean", "default": true}, "isForceFrontpage": {"type": "boolean", "default": false}, "isHideCurrentLang": {"type": "boolean", "default": false}, "isHideLangWithoutTranslation": {"type": "boolean", "default": false}}, "viewScript": "file:./view.js", "render": "file:./render.php"}