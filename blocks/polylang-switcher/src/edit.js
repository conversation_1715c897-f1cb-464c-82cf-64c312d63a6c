/**
 * Retrieves the translation of text.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-i18n/
 */
import {__} from '@wordpress/i18n';

/**
 * React hook that is used to mark the block wrapper element.
 * It provides all the necessary props like the class name.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops
 */
import {useBlockProps, InspectorControls} from '@wordpress/block-editor';
import {Panel, PanelBody, PanelRow, CheckboxControl} from '@wordpress/components';
import {useEffect, useState} from '@wordpress/element';
import apiFetch from '@wordpress/api-fetch';

/**
 * Lets webpack process CSS, SASS or SCSS files referenced in JavaScript files.
 * Those files can contain any CSS code that gets applied to the editor.
 *
 * @see https://www.npmjs.com/package/@wordpress/scripts#using-css
 */
import './editor.scss';
import value from "../../../../../../wp-includes/js/codemirror/csslint";

/**
 * The edit function describes the structure of your block in the context of the
 * editor. This represents what the editor will render when the block is used.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-edit-save/#edit
 *
 * @return {Element} Element to render.
 */
export default function Edit({attributes, setAttributes}) {
	const blockProps = useBlockProps();
	const [languages, setLanguages] = useState([]);

	useEffect(() => {
		apiFetch({path: '/polylang/v1/languages'}).then((data) => {
			setLanguages(data);
		});
	}, []);

	const svgArrow = (
		<svg id="desktop-arrow-dropdown" viewBox="0 0 8 5">
			<path d="M1.41421 0C0.523309 0 0.077142 1.07714 0.707107 1.70711L3.29289 4.29289C3.68342 4.68342 4.31658 4.68342 4.70711 4.29289L7.2929 1.70711C7.92286 1.07714 7.47669 0 6.58579 0H1.41421Z"/>
		</svg>
	)
	return (
		<>
			<InspectorControls>
				<Panel>
					<PanelBody title={__('Switcher Settings', 'polylang-switcher')}>
						<PanelRow>
							<CheckboxControl
								label="Displays as a dropdown"
								checked={attributes.isDropdown}
								onChange={() => setAttributes({isDropdown: !attributes.isDropdown})}
							/>
						</PanelRow>
						<PanelRow>
							<CheckboxControl
								label="Displays language names"
								checked={attributes.isName}
								onChange={() => setAttributes({isName: !attributes.isName})}
							/>
						</PanelRow>
						<PanelRow>
							<CheckboxControl
								label="Display flags"
								checked={attributes.isFlag}
								onChange={() => setAttributes({isFlag: !attributes.isFlag})}
							/>
						</PanelRow>
						<PanelRow>
							<CheckboxControl
								label="Forces link to front page"
								checked={attributes.isForceHomepage}
								onChange={() => setAttributes({isForceHomepage: !attributes.isForceHomepage})}
							/>
						</PanelRow>
						<PanelRow>
							<CheckboxControl
								label="Hides the current language"
								checked={attributes.isHideCurrentLang}
								onChange={() => setAttributes({isHideCurrentLang: !attributes.isHideCurrentLang})}
							/>
						</PanelRow>
						<PanelRow>
							<CheckboxControl
								label="Hides languages with no translation"
								checked={attributes.isHideLangWithoutTranslation}
								onChange={() => setAttributes({isHideLangWithoutTranslation: !attributes.isHideLangWithoutTranslation})}
							/>
						</PanelRow>
					</PanelBody>
				</Panel>
			</InspectorControls>
			<div {...blockProps}>
				<div className="language-switcher">
					<div className="current-language">
						<>
							{Object.values(languages).map(value => (
								value.current_language ? (
									<a href='#' className="lang-list__link" key={value.slug}>
										{attributes.isFlag && <img src={value.flag} alt={value.name}/>}
										<span className="lang-list__name"></span>
										{attributes.isName ? (
											<span className="lang-list__name">{value.name}</span>
										) : null}
										{svgArrow}
									</a>
								) : null
							))}
						</>
					</div>
					<div className="lang-list">
						<>
							{Object.values(languages).map(value => (
								attributes.isHideCurrentLang && value.current_language ? null :
									<div className="lang-list__item">
										<a href='#' className="lang-list__link" key={value.slug}>
											{attributes.isFlag && <img src={value.flag} alt={value.name}/>}
											{attributes.isName ? (
												<span className="lang-list__name">{value.name}</span>
											) : null}
										</a>
									</div>
							))}
						</>
					</div>
				</div>
			</div>
		</>
	);
}
