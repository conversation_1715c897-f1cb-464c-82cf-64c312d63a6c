/**
 * React hook that is used to mark the block wrapper element.
 * It provides all the necessary props like the class name.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops
 */
import {useBlockProps, InnerBlocks, RichText} from '@wordpress/block-editor';

/**
 * The save function defines the way in which the different attributes should
 * be combined into the final markup, which is then serialized by the block
 * editor into `post_content`.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-edit-save/#save
 *
 * @return {Element} Element to render.
 */
export default function save({attributes}) {
	const {questions, svgIcon, faqItem} = attributes;
	const faqSchema = {
		"@context": "https://schema.org",
		"@type": "FAQPage",
		"mainEntity": questions.map(item => ({
			"@type": "Question",
			"name": item.question,
			"acceptedAnswer": {
				"@type": "Answer",
				"text": item.answer
			}
		}))
	}
	return (<>
		<section className="bl-faq-section" {...useBlockProps.save()}>
			<InnerBlocks.Content/>
			{questions && questions.length > 0 && (<ul className="faq-list">
				{questions.map((item, index) => (item.question.trim() !== '' && (
					<li data-activeColor={faqItem.activeColor} key={index} className="faq-item"
						style={{background: faqItem.background, color: faqItem.color}}>

						<div className="faq-item__question">
							{item.question}
							<img className="faq-item__icon" src={svgIcon} alt='chevron'/>
						</div>
						<div className="faq-item__answer">
							{item.answer}
						</div>
					</li>)))}
			</ul>)}
		</section>
		<script type="application/ld+json">
			{JSON.stringify(faqSchema)}
		</script>
	</>);
}

