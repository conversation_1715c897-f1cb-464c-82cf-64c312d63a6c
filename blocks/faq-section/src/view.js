/**
 * Use this file for JavaScript code that you want to run in the front-end
 * on posts/pages that contain this block.
 *
 * When this file is defined as the value of the `viewScript` property
 * in `block.json` it will be enqueued on the front end of the site.
 *
 * Example:
 *
 * ```js
 * {
 *   "viewScript": "file:./view.js"
 * }
 * ```
 *
 * If you're not making any changes to this file because your project doesn't need any
 * JavaScript running in the front-end, then you should delete this file and remove
 * the `viewScript` property from `block.json`.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-metadata/#view-script
 */

/* eslint-disable no-console */
/* eslint-enable no-console */
const faqElements = document.querySelectorAll('.faq-list .faq-item');
let activeItem;
let activeAnswer;
let activeQuestion;
let activeColor;
let faqIcon;
faqElements.forEach(element => {
	element.addEventListener('click', (e) => {
		activeItem = e.currentTarget;
		activeItem.classList.toggle('active');
		activeQuestion = activeItem.querySelector('.faq-item__question');
		activeAnswer = activeItem.querySelector('.faq-item__answer');
		faqIcon = activeItem.querySelector('.faq-item__icon');

		activeColor = activeItem.dataset.activecolor;
		if (activeAnswer.style.maxHeight) {
			activeAnswer.removeAttribute('style');
			activeQuestion.removeAttribute('style');
			faqIcon.classList.remove("rotated");

		} else {
			activeQuestion.style.color = activeColor;
			activeAnswer.style.marginTop = '10px';
			activeAnswer.style.maxHeight = activeAnswer.scrollHeight + "px";
			faqIcon.classList.add('rotated');
		}
		faqElements.forEach(el => {
			if (el !== activeItem) {
				el.classList.remove('active');
				el.querySelector('.faq-item__answer').removeAttribute('style');
				el.querySelector('.faq-item__question').removeAttribute('style');
				el.querySelector('.faq-item__icon').classList.remove('rotated');
			}
		})
	})
})

