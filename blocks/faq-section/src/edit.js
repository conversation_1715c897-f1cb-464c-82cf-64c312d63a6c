/**
 * Retrieves the translation of text.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-i18n/
 */
import {__} from '@wordpress/i18n';

/**
 * React hook that is used to mark the block wrapper element.
 * It provides all the necessary props like the class name.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops
 */
import {
	useBlockProps,
	MediaUpload,
	MediaUploadCheck
} from '@wordpress/block-editor';
import {
	Panel,
	PanelRow,
	PanelBody,
	Button,
	ColorPalette,
} from '@wordpress/components';
import {
	InspectorControls,
	InnerBlocks,
	RichText
} from '@wordpress/block-editor';

/**
 * Lets webpack process CSS, SASS or SCSS files referenced in JavaScript files.
 * Those files can contain any CSS code that gets applied to the editor.
 *
 * @see https://www.npmjs.com/package/@wordpress/scripts#using-css
 */
import './editor.scss';

/**
 * The edit function describes the structure of your block in the context of the
 * editor. This represents what the editor will render when the block is used.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-edit-save/#edit
 *
 * @return {Element} Element to render.
 */
export default function Edit(props) {

	const blockProps = useBlockProps();

	const {attributes, setAttributes} = props;

	const {questions, svgIcon, faqItem} = attributes;

	const addQuestion = () => {
		const newQuestions = [...questions, {question: '', answer: ''}];
		setAttributes({questions: newQuestions});
	}
	const removeQuestion = (index) => {
		const newQuestions = questions.filter((item, i) => i !== index);
		setAttributes({questions: newQuestions});
	};
	const updateQuestion = (value, index, field) => {
		const newQuestions = questions.map((item, i) => {
			if (i === index) {
				item[field] = value;
			}
			return item;
		});
		setAttributes({questions: newQuestions});
	};

	const updateColor = (field, newColor) => {
		setAttributes({ ...attributes, faqItem: { ...faqItem, [field]: newColor } });
	};
	const onSelectSvg = (media) => {
		setAttributes({svgIcon: media.url})
	}
	const faqContentTemplate = [
		['core/heading', {placeholder: 'Faq section heading'}],
		['core/paragraph', {placeholder: 'Faq section paragraph'}],
	];
	return (
		<>
			<InspectorControls>
				<Panel>
					<PanelBody title="Faq icon" initialOpen={true}>
						<PanelRow>
							<MediaUploadCheck>
								<MediaUpload
									onSelect={onSelectSvg}
									allowedTypes={['image/svg+xml']}
									render={({open}) => (
										<Button onClick={open} variant="primary">
											{__('Select SVG Icon', 'faq-block')}
										</Button>
									)}
								/>
							</MediaUploadCheck>
							{svgIcon && (
								<div className="faq-svg-icon">
									<img src={svgIcon} alt={__('SVG Icon', 'faq-block')}/>
								</div>
							)}
						</PanelRow>
					</PanelBody>
					<PanelBody title="Color" initialOpen={true}>
						<PanelRow>
							<ColorPalette
								value={faqItem.color}
								onChange={(newColor) => updateColor('color', newColor)}
							/>
						</PanelRow>
					</PanelBody>
					<PanelBody title="Background" initialOpen={true}>
						<PanelRow>
							<ColorPalette
								value={faqItem.background}
								onChange={(newColor) => updateColor('background', newColor)}
							/>
						</PanelRow>
					</PanelBody>
					<PanelBody title="Active" initialOpen={true}>
						<PanelRow>
							<ColorPalette
								value={faqItem.activeColor}
								onChange={(newColor) => updateColor('activeColor', newColor)}
							/>
						</PanelRow>
					</PanelBody>
				</Panel>
			</InspectorControls>
			<section className="bl-faq-section" {...blockProps}>
				<InnerBlocks template={faqContentTemplate} allowedBlocks={[
					'core/heading',
					'core/paragraph',
				]}/>
				{questions && questions.length > 0 && (
					<ul className="faq-list">
						{questions.map((item, index) => (
							<li data-activeColor={faqItem.activeColor} key={index} className="faq-item" style={{background: faqItem.background, color: faqItem.color}} >
								<RichText
									tagName="div"
									className="faq-item__question"
									placeholder={__('Please enter a question', 'faq-block')}
									value={item.question}
									onChange={(value) => updateQuestion(value, index, 'question')}
								/>
								<RichText
									tagName="div"
									className="faq-item__answer"
									placeholder={__('Please enter an answer', 'faq-block')}
									value={item.answer}
									onChange={(value) => updateQuestion(value, index, 'answer')}
								/>
								<Button className="delete-btn" onClick={() => removeQuestion(index)}>
									{__('Delete FAQ', 'faq-block')}
								</Button>
							</li>
						))}
					</ul>
				)}
				<Button className="add-btn" isPrimary onClick={addQuestion}>
					{__('Add FAQ', 'faq-block')}
				</Button>
			</section>
		</>

	);
}
