<?php
if ( ! wp_style_is( 'faq_variant_1.css', 'enqueued' ) ) {
	$file_url = get_template_directory_uri() . '/template-parts/variants-parts/faq/css/faq_variant_1.css';
	wp_enqueue_style( 'faq_variant_1.css', $file_url );
}
$padding                  = get_field( 'section_padding_smalls', 'option' );
$padding                  = 'padding: ' . $padding['top_padding'] . 'px 0 ' . $padding['bottom_padding'] . 'px;';
$section_background_color = get_sub_field( 'section_background_color' );
$section_text_color       = get_sub_field( 'section_text_color' );
$section_text             = get_sub_field( 'section_text' );
$items                    = get_sub_field( 'faqs_repeater' );
$num_items                = count( $items );
$align_section_text       = get_sub_field( 'align_section_text' );
// Pattern for preg_replace method for Schema FAQ layout.
$double_quotes_pattern = '/"/i';


if ( $section_background_color ) {
	$section_background_color_css = 'background-color: ' . $section_background_color . '; ';
}
if ( $section_text_color ) {
	$section_text_color_css = 'color: ' . $section_text_color . '; ';
}
$unique_id = 'faq_1_' . get_row_index() . get_the_ID();

?>
<section class="faq_1 padding_small" id="<?php echo esc_html( $unique_id ); ?>"
	<?php
	if ( $section_background_color || $section_text_color ) {
		echo 'style="' . $section_background_color_css . $section_text_color_css . '"';
	}
	?>
>
	<div class="container">
		<div class="section_text">
			<?php if ( $section_text ) : ?>
				<div><?php echo wp_kses_post( $section_text ); ?></div>
			<?php endif; ?>
		</div>
		<ul class="accordion_list">
			<?php
			if ( $items ) :
				foreach ( $items as $item ) :
					?>
					<li class="item">
						<?php if ( $item['question'] ) : ?>
							<div class="item_question"
								<?php
								if ( $section_text_color ) {
									echo( 'style=" ' . $section_text_color_css . '"' );
								}
								?>
							><?php echo wp_kses_post( $item['question'] ); ?>
								<div class="plus_box">
									<svg class="plus_box_icon" version="1.1" xmlns="http://www.w3.org/2000/svg"
										 xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
										 width="19px" height="9.057px" viewBox="0 0 19 9.057" xml:space="preserve">
											<g>
												<path d="M9.5,9.057L0.376,1.782c-0.432-0.344-0.502-0.973-0.158-1.405c0.345-0.431,0.975-0.502,1.405-0.158L9.5,6.499l7.876-6.281
													c0.432-0.345,1.061-0.273,1.405,0.158c0.344,0.432,0.273,1.061-0.158,1.405L9.5,9.057z"/>
											</g>
										</svg>
								</div>
							</div>
						<?php endif; ?>
						<div class="item_answer"><?php echo wp_kses_post( $item['answer'] ); ?></div>
					</li>
				<?php
				endforeach;
			endif;
			?>
		</ul>
	</div><!-- end container -->
	<script type="application/ld+json">
		{
			"@context": "https://schema.org",
			"@type": "FAQPage",
			"mainEntity": [
		<?php
		if ( $items ) :
			$i = 0;
			foreach ( $items as $item ) :
				$string_with_backslashes = preg_replace( $double_quotes_pattern, '\"', $item['answer'] );
				?>
				<?php if ( ++ $i === $num_items ) : ?>
								{
									"@type": "Question",
									"name": "<?php echo wp_kses_post( $item['question'] ); ?>",
									"acceptedAnswer": {
									"@type": "Answer",
									"text": "<?php echo $string_with_backslashes; ?>"
									}
								}
							<?php else : ?>
								{
									"@type": "Question",
									"name": "<?php echo wp_kses_post( $item['question'] ); ?>",
									"acceptedAnswer": {
									"@type": "Answer",
									"text": "<?php echo $string_with_backslashes; ?>"
									}
								},
							<?php endif; ?>
			<?php
			endforeach;
		endif;
		?>
		]
	}

	</script>

</section><!-- end faq_1 -->
