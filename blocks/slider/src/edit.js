/**
 * Retrieves the translation of text.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-i18n/
 */
import {__} from '@wordpress/i18n';

/**
 * React hook that is used to mark the block wrapper element.
 * It provides all the necessary props like the class name.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops
 */
import {
	useBlockProps,
	InspectorControls,
	InnerBlocks,
	MediaUpload,
	MediaUploadCheck
} from '@wordpress/block-editor';
import {
	Panel,
	PanelRow,
	PanelBody,
	Button,
} from '@wordpress/components';
/**
 * Lets webpack process CSS, SASS or SCSS files referenced in JavaScript files.
 * Those files can contain any CSS code that gets applied to the editor.
 *
 * @see https://www.npmjs.com/package/@wordpress/scripts#using-css
 */
import './editor.scss';

/**
 * The edit function describes the structure of your block in the context of the
 * editor. This represents what the editor will render when the block is used.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-edit-save/#edit
 *
 * @return {Element} Element to render.
 */
export default function Edit(props) {
	const {attributes, setAttributes} = props;
	const {svgIconLeft, svgIconRight} = attributes;

	const onSelectSvgLeft = (media) => {
		setAttributes({svgIconLeft: media.url})
	}
	const onSelectSvgRight = (media) => {
		setAttributes({svgIconRight: media.url})
	}
	return (
		<>
			<InspectorControls>
				<Panel>
					<PanelBody title="Slider arrow left" initialOpen={true}>
						<PanelRow>
							<MediaUploadCheck>
								<MediaUpload
									onSelect={onSelectSvgLeft}
									allowedTypes={['image/svg+xml']}
									render={({open}) => (
										<Button onClick={open} variant="primary">
											{__('Select SVG Icon', 'slider')}
										</Button>
									)}
								/>
							</MediaUploadCheck>
						</PanelRow>
					</PanelBody>
					<PanelBody title="Slider arrow right" initialOpen={true}>
						<PanelRow>
							<MediaUploadCheck>
								<MediaUpload
									onSelect={onSelectSvgRight}
									allowedTypes={['image/svg+xml']}
									render={({open}) => (
										<Button onClick={open} variant="primary">
											{__('Select SVG Icon', 'slider')}
										</Button>
									)}
								/>
							</MediaUploadCheck>
						</PanelRow>
					</PanelBody>
				</Panel>
			</InspectorControls>
			<section {...useBlockProps()}>
				<div className="swiper mySwiper">
						<InnerBlocks/>
				</div>
			</section>
		</>

	);
}
