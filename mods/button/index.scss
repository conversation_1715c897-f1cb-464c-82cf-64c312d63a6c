.wp-block-button[class*=has-icon__] {
    .wp-block-button__link {
        display: flex;
        gap: 0.5em;
        align-items: center;

        span {
            line-height: 0;
        }

        svg {
            color: currentColor;
            fill: currentColor;
        }
    }
    &.has-icon__arrow-big-right{
        svg {
            height: 24px;
            width: 44px;
            fill: transparent;
        }
    }
    &.has-icon__arrow-bold-right{
        svg {
            height: 24px;
            width: 46px;
            fill: transparent;

        }
    }
    &.has-icon__arrow-short-right{
        svg {
            height: 25px;
            width: 24px;
            fill: transparent;
        }
    }

    &.has-icon__arrow-left,
    &.has-icon__arrow-right,
    &.has-icon__cloud,
    &.has-icon__cloud-upload {
        svg {
            height: 0.7em;
            width: 1em;
        }
    }

    &.has-icon__chevron-left,
    &.has-icon__chevron-right {
        svg {
            height: 0.8em;
            width: 0.45em;
        }
    }

    &.has-icon__chevron-left-small,
    &.has-icon__chevron-right-small {
        svg {
            height: 0.5em;
            width: 0.3em;
        }
    }

    &.has-icon__comment-author-avatar,
    &.has-icon__help,
    &.has-icon__info {
        svg {
            height: 0.9em;
            width: 0.9em;
        }
    }

    &.has-icon__download {
        svg {
            height: 0.8em;
            width: 0.75em;
        }
    }

    &.has-icon__external {
        svg {
            height: 0.7em;
            width: 0.7em;
        }
    }

    &.has-icon__external-arrow {
        svg {
            height: 0.6em;
            width: 0.6em;
        }
    }

    &.has-icon__lock-outline {
        svg {
            height: 0.9em;
            width: 0.64em;
        }
    }

    &.has-icon__login {
        svg {
            height: 0.8em;
            width: 0.8em;
        }
    }

    &.has-icon__next,
    &.has-icon__previous {
        svg {
            height: 0.75em;
            width: 0.775em;
        }
    }

    &.has-icon__shuffle,
    &.has-icon__wordpress {
        svg {
            height: 0.9em;
            width: 0.9em;
        }
    }
  &.has-icon__linkedin,
  &.has-icon__instagram,
  &.has-icon__telegram,
  &.has-icon__whatsapp{
    svg {
      height: 24px;
      width: 24px;
    }
  }
}
.wp-block-button__link{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  //padding: 20px 50px;
}

.wp-block-button__link.has-only-icon {
 span{
   display: block!important;
   height: 25px;
   width: 25px;
 }
}
.wp-block-button__link{
  span.is-left{
   margin-right: 5px;
  }
}
.wp-block-button__link.is-right {
  span.is-right{
    margin-left: 5px;
  }
}



/* Generic rule for all button icons */
.wp-block-button__link .button-icon svg {
  display: inline-block;
  height: 1em;       /* control size via font-size of the container */
  width: auto;       /* computed from the SVG viewBox */
  vertical-align: middle; /* nicer alignment */
  aspect-ratio: auto;     /* modern browsers, but not strictly required */
  max-width: 100%;
  max-height: 100%;
  fill: currentColor;     /* for filled icons */
  /* If some icons are strokes only, you can prefer these:
     fill: none;
     stroke: currentColor;
  */
}

/* If some SVGs still have width/height attributes you cannot remove,
   force CSS to win (prefer to fix markup instead of using !important) */
.wp-block-button__link .button-icon svg {
  height: 1em !important;
  width: auto !important;
}

/* Icon-only buttons: control the overall pixel size by font-size on the wrapper */
.wp-block-button__link.has-only-icon .button-icon {
  display: inline-flex;
  line-height: 1;
  font-size: 24px;  /* pick your base size; the SVG will be 1em x auto */
}

