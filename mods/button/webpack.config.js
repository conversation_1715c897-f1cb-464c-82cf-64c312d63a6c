/**
 * External dependencies
 */
const RemoveEmptyScriptsPlugin = require( 'webpack-remove-empty-scripts' );
const path = require( 'path' );
const defaultConfig = require( '@wordpress/scripts/config/webpack.config' );

module.exports = {
	...defaultConfig,

	entry: {
		'index' : path.resolve( process.cwd(), 'index.js' ),
		'editor' : path.resolve( process.cwd(), 'editor.scss' ),
	    'style': path.resolve( process.cwd(), 'index.scss' ),
	},

	plugins: [
		...defaultConfig.plugins,
        new RemoveEmptyScriptsPlugin(),
	],
};