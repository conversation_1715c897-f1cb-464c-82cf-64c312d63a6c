/**
 * External dependencies
 */
import classnames from 'classnames';

/**
 * WordPress dependencies
 */
import {__} from '@wordpress/i18n';
import {addFilter} from '@wordpress/hooks';
import {InspectorControls} from '@wordpress/block-editor';
import {
    Button,
    PanelBody,
    PanelRow,
    ToggleControl,
    UnitControl,
    __experimentalGrid as Grid, // eslint-disable-line
} from '@wordpress/components';
import {
    arrowRight,
    arrowLeft,
    chevronLeft,
    chevronLeftSmall,
    chevronRight,
    chevronRightSmall,
    cloud,
    cloudUpload,
    commentAuthorAvatar,
    download,
    external,
    help,
    info,
    lockOutline,
    login,
    next,
    previous,
    shuffle,
    wordpress,
} from '@wordpress/icons';

/**
 * All available icons.
 * (Order determines presentation order)
 */
export const ICONS = [
    {
        label: __('Chevron Right', 'enable-button-icons'),
        value: 'chevron-right',
        icon: (chevronRight),
    },
    {
        label: __('Chevron Left', 'enable-button-icons'),
        value: 'chevron-left',
        icon: chevronLeft,
    },
    {
        label: __('Chevron Right (Small)', 'enable-button-icons'),
        value: 'chevron-right-small',
        icon: chevronRightSmall,
    },
    {
        label: __('Chevron Left (Small)', 'enable-button-icons'),
        value: 'chevron-left-small',
        icon: chevronLeftSmall,
    },
    {
        label: __('Shuffle', 'enable-button-icons'),
        value: 'shuffle',
        icon: shuffle,
    },
    {
        label: __('Arrow Right', 'enable-button-icons'),
        value: 'arrow-right',
        icon: arrowRight,
    },
    {
        label: __('Arrow Left', 'enable-button-icons'),
        value: 'arrow-left',
        icon: arrowLeft,
    },
    {
        label: __('Next', 'enable-button-icons'),
        value: 'next',
        icon: next,
    },
    {
        label: __('Previous', 'enable-button-icons'),
        value: 'previous',
        icon: previous,
    },
    {
        label: __('Download', 'enable-button-icons'),
        value: 'download',
        icon: download,
    },
    {
        label: __('External Arrow', 'enable-button-icons'),
        value: 'external-arrow',
        icon: (
            <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <polygon
                    points="18 6 8.15240328 6 8.15240328 8.1101993 14.3985932 8.1101993 6 16.5087925 7.4912075 18 15.8898007 9.6014068 15.8898007 15.8475967 18 15.8475967"></polygon>
            </svg>
        ),
    },
    {
        label: __('External', 'enable-button-icons'),
        value: 'external',
        icon: external,
    },
    {
        label: __('Login', 'enable-button-icons'),
        value: 'login',
        icon: login,
    },
    {
        label: __('Lock', 'enable-button-icons'),
        value: 'lock-outline',
        icon: lockOutline,
    },
    {
        label: __('Avatar', 'enable-button-icons'),
        value: 'comment-author-avatar',
        icon: commentAuthorAvatar,
    },
    {
        label: __('Cloud', 'enable-button-icons'),
        value: 'cloud',
        icon: cloud,
    },
    {
        label: __('Cloud Upload', 'enable-button-icons'),
        value: 'cloud-upload',
        icon: cloudUpload,
    },
    {
        label: __('Help', 'enable-button-icons'),
        value: 'help',
        icon: help,
    },
    {
        label: __('Info', 'enable-button-icons'),
        value: 'info',
        icon: info,
    },
    {
        label: __('WordPress', 'enable-button-icons'),
        value: 'wordpress',
        icon: wordpress,
    },
    {
        label: __('Arrow Big Right', 'enable-button-icons'),
        value: 'arrow-big-right',
        icon: (
            <svg width="46" height="24" viewBox="0 0 46 24" fill="transparent" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12L41 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
                <path d="M34 5L41 12L34 19" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
            </svg>
        ),
    },
    {
        label: __('Arrow Bold Right', 'enable-button-icons'),
        value: 'arrow-bold-right',
        icon: (
            <svg width="46" height="24" viewBox="0 0 46 24" fill="transparent" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12L41 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
                <path d="M34 5L41 12L34 19" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
            </svg>

        ),
    },
    {
        label: __('Arrow Short Right', 'enable-button-icons'),
        value: 'arrow-short-right',
        icon: (
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="arrow-tail" d="M5 12.5H19" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
                <path d="M12 5.5L19 12.5L12 19.5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
            </svg>
        ),
    },
    {
        label: __('Linkedin', 'enable-button-icons'),
        value: 'linkedin',
        icon: (
            <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                <g>
                    <path
                        d="M24.25 23.7547V14.8807C24.25 10.1257 21.712 7.91323 18.328 7.91323C15.5972 7.91323 14.3733 9.41548 13.6915 10.47V8.27698H8.5465C8.61475 9.72973 8.5465 23.7547 8.5465 23.7547H13.6915V15.111C13.6915 14.6497 13.7245 14.187 13.861 13.857C14.2323 12.9322 15.079 11.9752 16.4995 11.9752C18.3617 11.9752 19.1058 13.3942 19.1058 15.4747V23.7555L24.25 23.7547ZM3.12625 6.16423C4.9195 6.16423 6.03775 4.97473 6.03775 3.48898C6.00475 1.97098 4.92025 0.815979 3.16075 0.815979C1.40125 0.815979 0.25 1.97023 0.25 3.48898C0.25 4.97473 1.36675 6.16423 3.094 6.16423H3.12625ZM5.69875 23.7547V8.27698H0.5545V23.7547H5.69875Z"/>
                </g>
            </svg>
        ),
    },
    {
        label: __('Instagram', 'enable-button-icons'),
        value: 'instagram',
        icon: (
            <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M7.25 1.28571C4.21243 1.28571 1.75 3.74814 1.75 6.78571V17.7857C1.75 20.8233 4.21243 23.2857 7.25 23.2857H18.25C21.2876 23.2857 23.75 20.8233 23.75 17.7857V6.78571C23.75 3.74814 21.2876 1.28571 18.25 1.28571H7.25ZM12.75 8.28571C10.5409 8.28571 8.75 10.0766 8.75 12.2857C8.75 14.4948 10.5409 16.2857 12.75 16.2857C14.9591 16.2857 16.75 14.4948 16.75 12.2857C16.75 10.0766 14.9591 8.28571 12.75 8.28571ZM6.75 12.2857C6.75 8.972 9.43629 6.28571 12.75 6.28571C16.0637 6.28571 18.75 8.972 18.75 12.2857C18.75 15.5994 16.0637 18.2857 12.75 18.2857C9.43629 18.2857 6.75 15.5994 6.75 12.2857ZM18.75 4.28571C17.6454 4.28571 16.75 5.18114 16.75 6.28571C16.75 7.39028 17.6454 8.28571 18.75 8.28571C19.8546 8.28571 20.75 7.39028 20.75 6.28571C20.75 5.18114 19.8546 4.28571 18.75 4.28571Z"/>
            </svg>
        ),
    },
    {
        label: __('Telegram', 'enable-button-icons'),
        value: 'telegram',
        icon: (
            <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M9.66718 15.4667L9.27018 21.0507C9.83818 21.0507 10.0842 20.8067 10.3792 20.5137L13.0422 17.9687L18.5602 22.0097C19.5722 22.5737 20.2852 22.2767 20.5582 21.0787L24.1802 4.10668L24.1812 4.10568C24.5022 2.60968 23.6402 2.02468 22.6542 2.39168L1.36418 10.5427C-0.0888222 11.1067 -0.0668222 11.9167 1.11718 12.2837L6.56018 13.9767L19.2032 6.06568C19.7982 5.67168 20.3392 5.88968 19.8942 6.28368L9.66718 15.4667Z"/>
            </svg>
        ),
    },
    {
        label: __('Whatsapp', 'enable-button-icons'),
        value: 'whatsapp',
        icon: (
            <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M12.8375 0.285706C6.37563 0.285706 1.11852 5.54281 1.11852 12.0046C1.11852 14.2119 1.7308 16.3479 2.89304 18.2069L0.990174 23.2918C0.889132 23.562 0.955071 23.8662 1.15899 24.0702C1.29955 24.2106 1.48766 24.2857 1.67951 24.2857C1.76605 24.2857 1.85333 24.2705 1.9373 24.239L7.18896 22.2738C8.91271 23.2233 10.8578 23.7235 12.8374 23.7235C19.2993 23.7235 24.5564 18.4664 24.5564 12.0046C24.5564 5.54281 19.2994 0.285706 12.8375 0.285706ZM18.4844 15.6847C18.1572 16.8484 16.9339 17.6928 15.5757 17.6928C15.4429 17.6928 15.3087 17.6845 15.179 17.6684C13.5282 17.4311 11.5963 16.2985 10.0074 14.6349C8.33413 13.0368 7.20155 11.1052 6.97761 9.46769C6.75095 7.8104 7.49253 6.5745 8.96113 6.1615C9.03656 6.14031 9.11634 6.12956 9.19824 6.12956C10.0585 6.12956 11.0833 7.3453 11.4393 8.21685C11.6711 8.78446 11.6535 9.22175 11.3885 9.4816C10.7235 10.1333 10.4757 10.5295 10.5043 10.8949C10.5364 11.3021 10.9154 11.7726 11.5592 12.4476C11.6413 12.5338 11.7513 12.6486 11.8748 12.7726C11.9901 12.8875 12.1006 12.9934 12.1984 13.0869C12.9171 13.7723 13.3865 14.1438 13.8082 14.1438C14.1588 14.1437 14.539 13.8952 15.1641 13.2574C15.2588 13.1608 15.4335 13.0458 15.7277 13.0458C16.5035 13.0458 17.5983 13.8029 18.1341 14.5101C18.4589 14.9385 18.58 15.3447 18.4844 15.6847Z"/>
            </svg>
        ),
    },
];

/**
 * Add the attributes needed for button icons.
 *
 * @since 0.1.0
 * @param {Object} settings
 */
function addAttributes(settings) {
    if ('core/button' !== settings.name) {
        return settings;
    }

    // Add the block visibility attributes.
    const iconAttributes = {
        icon: {
            type: 'string',
        },
        iconPositionLeft: {
            type: 'boolean',
            default: false,
        },
        isOnlyIcon: {
            type: 'boolean',
            default: false,
        },
        iconHeight: {
            type: 'string',
            default: '1em',
        },
    };

    const newSettings = {
        ...settings,
        attributes: {
            ...settings.attributes,
            ...iconAttributes,
        },
    };

    return newSettings;
}

addFilter(
    'blocks.registerBlockType',
    'enable-button-icons/add-attributes',
    addAttributes
);

/**
 * Filter the BlockEdit object and add icon inspector controls to button blocks.
 *
 * @since 0.1.0
 * @param {Object} BlockEdit
 */
function addInspectorControls(BlockEdit) {
    return (props) => {
        if (props.name !== 'core/button') {
            return <BlockEdit {...props} />;
        }

        const {attributes, setAttributes} = props;
        const {icon: currentIcon, iconPositionLeft, isOnlyIcon, iconHeight} = attributes;

        return (
            <>
                <BlockEdit {...props} />
                <InspectorControls>
                    <PanelBody
                        title={__('Icon settings', 'enable-button-icons')}
                        className="button-icon-picker"
                        initialOpen={true}
                    >
                        <PanelRow>
                            <Grid
                                className="button-icon-picker__grid"
                                columns="5"
                                gap="1"
                            >
                                {ICONS.map((icon, index) => (
                                    <Button
                                        key={index}
                                        label={icon?.label}
                                        isPressed={currentIcon === icon.value}
                                        className="button-icon-picker__button"
                                        onClick={() =>
                                            setAttributes({
                                                // Allow user to disable icons.
                                                icon:
                                                    currentIcon === icon.value
                                                        ? null
                                                        : icon.value,
                                            })
                                        }
                                    >
                                        {icon.icon ?? icon.value}
                                    </Button>
                                ))}
                            </Grid>
                        </PanelRow>
                        <PanelRow>
                            <UnitControl
                                label={__('Icon height', 'legendsblocks')}
                                onChange={(next) => setAttributes({iconHeight: next || ''})}
                                units={[
                                    {value: 'px', label: 'px'},
                                    {value: 'em', label: 'em'},
                                    {value: 'rem', label: 'rem'},
                                ]}
                                help={__('Set only the height; width will follow the SVG viewBox.', 'legendsblocks')}
                                min={0}
                            />

                        </PanelRow>
                        <PanelRow>
                            <ToggleControl
                                label={__(
                                    'Show icon on left',
                                    'enable-button-icons'
                                )}
                                checked={iconPositionLeft}
                                onChange={() => {
                                    setAttributes({
                                        iconPositionLeft: !iconPositionLeft,
                                    });
                                }}
                            />
                        </PanelRow>
                        <ToggleControl
                            label={__(
                                'Show only Icon',
                                'enable-button-icons'
                            )}
                            checked={isOnlyIcon}
                            onChange={() => {
                                setAttributes({
                                    isOnlyIcon: !isOnlyIcon,
                                });
                            }}
                        />
                    </PanelBody>
                </InspectorControls>
            </>
        );
    };
}

addFilter(
    'editor.BlockEdit',
    'enable-button-icons/add-inspector-controls',
    addInspectorControls
);

/**
 * Add icon and position classes in the Editor.
 *
 * @since 0.1.0
 * @param {Object} BlockListBlock
 */
function setAttributes(BlockListBlock) {
    return (props) => {
        const {name, attributes} = props;

        if ('core/button' !== name || !attributes?.icon) {
            return <BlockListBlock {...props} />;
        }

        const classes = classnames(props?.className, {
            [`has-icon__${attributes?.icon}`]: attributes?.icon,
            'has-icon-position__left': attributes?.iconPositionLeft,
            'has-icon-only': attributes?.isOnlyIcon,
        });
        if (attributes?.iconHeight) {
            const style = {
                '--icon-height': attributes?.iconHeight,
            };
            return <BlockListBlock {...props} className={classes} wrapperProps={style}/>;
        }

        return <BlockListBlock {...props} className={classes}/>;
    };
}

addFilter(
    'editor.BlockListBlock',
    'enable-button-icons/add-classes',
    setAttributes
);
