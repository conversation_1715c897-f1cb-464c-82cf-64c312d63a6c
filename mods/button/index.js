/**
 * External dependencies
 */
import classnames from 'classnames';

/**
 * WordPress dependencies
 */
import {__} from '@wordpress/i18n';
import {addFilter} from '@wordpress/hooks';
import {InspectorControls} from '@wordpress/block-editor';
import {
    Button,
    PanelBody,
    PanelRow,
    ToggleControl,
    __experimentalUnitControl as UnitControl,
    __experimentalGrid as Grid, // eslint-disable-line
} from '@wordpress/components';
import {
    arrowRight,
    arrowLeft,
    chevronLeft,
    chevronLeftSmall,
    chevronRight,
    chevronRightSmall,
    cloud,
    cloudUpload,
    commentAuthorAvatar,
    download,
    external,
    help,
    info,
    lockOutline,
    login,
    next,
    previous,
    shuffle,
    wordpress,
} from '@wordpress/icons';

/**
 * All available icons.
 * (Order determines presentation order)
 */
export const ICONS = [
    {
        label: __('Chevron Right', 'enable-button-icons'),
        value: 'chevron-right',
        icon: (chevronRight),
    },
    {
        label: __('Chevron Left', 'enable-button-icons'),
        value: 'chevron-left',
        icon: chevronLeft,
    },
    {
        label: __('Chevron Right (Small)', 'enable-button-icons'),
        value: 'chevron-right-small',
        icon: chevronRightSmall,
    },
    {
        label: __('Chevron Left (Small)', 'enable-button-icons'),
        value: 'chevron-left-small',
        icon: chevronLeftSmall,
    },
    {
        label: __('Shuffle', 'enable-button-icons'),
        value: 'shuffle',
        icon: shuffle,
    },
    {
        label: __('Arrow Right', 'enable-button-icons'),
        value: 'arrow-right',
        icon: arrowRight,
    },
    {
        label: __('Arrow Left', 'enable-button-icons'),
        value: 'arrow-left',
        icon: arrowLeft,
    },
    {
        label: __('Next', 'enable-button-icons'),
        value: 'next',
        icon: next,
    },
    {
        label: __('Previous', 'enable-button-icons'),
        value: 'previous',
        icon: previous,
    },
    {
        label: __('Download', 'enable-button-icons'),
        value: 'download',
        icon: download,
    },
    {
        label: __('External Arrow', 'enable-button-icons'),
        value: 'external-arrow',
        icon: (
            <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <polygon
                    points="18 6 8.15240328 6 8.15240328 8.1101993 14.3985932 8.1101993 6 16.5087925 7.4912075 18 15.8898007 9.6014068 15.8898007 15.8475967 18 15.8475967"></polygon>
            </svg>
        ),
    },
    {
        label: __('External', 'enable-button-icons'),
        value: 'external',
        icon: external,
    },
    {
        label: __('Login', 'enable-button-icons'),
        value: 'login',
        icon: login,
    },
    {
        label: __('Lock', 'enable-button-icons'),
        value: 'lock-outline',
        icon: lockOutline,
    },
    {
        label: __('Avatar', 'enable-button-icons'),
        value: 'comment-author-avatar',
        icon: commentAuthorAvatar,
    },
    {
        label: __('Cloud', 'enable-button-icons'),
        value: 'cloud',
        icon: cloud,
    },
    {
        label: __('Cloud Upload', 'enable-button-icons'),
        value: 'cloud-upload',
        icon: cloudUpload,
    },
    {
        label: __('Help', 'enable-button-icons'),
        value: 'help',
        icon: help,
    },
    {
        label: __('Info', 'enable-button-icons'),
        value: 'info',
        icon: info,
    },
    {
        label: __('WordPress', 'enable-button-icons'),
        value: 'wordpress',
        icon: wordpress,
    },
    {
        label: __('Arrow Big Right', 'enable-button-icons'),
        value: 'arrow-big-right',
        icon: (
            <svg width="46" height="24" viewBox="0 0 46 24" fill="transparent" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12L41 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
                <path d="M34 5L41 12L34 19" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
            </svg>
        ),
    },
    {
        label: __('Arrow Bold Right', 'enable-button-icons'),
        value: 'arrow-bold-right',
        icon: (
            <svg width="46" height="24" viewBox="0 0 46 24" fill="transparent" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12L41 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
                <path d="M34 5L41 12L34 19" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
            </svg>

        ),
    },
    {
        label: __('Arrow Short Right', 'enable-button-icons'),
        value: 'arrow-short-right',
        icon: (
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="arrow-tail" d="M5 12.5H19" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
                <path d="M12 5.5L19 12.5L12 19.5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
            </svg>
        ),
    },
    {
        label: __('Linkedin', 'enable-button-icons'),
        value: 'linkedin',
        icon: (
            <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                <g>
                    <path
                        d="M24.25 23.7547V14.8807C24.25 10.1257 21.712 7.91323 18.328 7.91323C15.5972 7.91323 14.3733 9.41548 13.6915 10.47V8.27698H8.5465C8.61475 9.72973 8.5465 23.7547 8.5465 23.7547H13.6915V15.111C13.6915 14.6497 13.7245 14.187 13.861 13.857C14.2323 12.9322 15.079 11.9752 16.4995 11.9752C18.3617 11.9752 19.1058 13.3942 19.1058 15.4747V23.7555L24.25 23.7547ZM3.12625 6.16423C4.9195 6.16423 6.03775 4.97473 6.03775 3.48898C6.00475 1.97098 4.92025 0.815979 3.16075 0.815979C1.40125 0.815979 0.25 1.97023 0.25 3.48898C0.25 4.97473 1.36675 6.16423 3.094 6.16423H3.12625ZM5.69875 23.7547V8.27698H0.5545V23.7547H5.69875Z"/>
                </g>
            </svg>
        ),
    },
    {
        label: __('Instagram', 'enable-button-icons'),
        value: 'instagram',
        icon: (
            <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M7.25 1.28571C4.21243 1.28571 1.75 3.74814 1.75 6.78571V17.7857C1.75 20.8233 4.21243 23.2857 7.25 23.2857H18.25C21.2876 23.2857 23.75 20.8233 23.75 17.7857V6.78571C23.75 3.74814 21.2876 1.28571 18.25 1.28571H7.25ZM12.75 8.28571C10.5409 8.28571 8.75 10.0766 8.75 12.2857C8.75 14.4948 10.5409 16.2857 12.75 16.2857C14.9591 16.2857 16.75 14.4948 16.75 12.2857C16.75 10.0766 14.9591 8.28571 12.75 8.28571ZM6.75 12.2857C6.75 8.972 9.43629 6.28571 12.75 6.28571C16.0637 6.28571 18.75 8.972 18.75 12.2857C18.75 15.5994 16.0637 18.2857 12.75 18.2857C9.43629 18.2857 6.75 15.5994 6.75 12.2857ZM18.75 4.28571C17.6454 4.28571 16.75 5.18114 16.75 6.28571C16.75 7.39028 17.6454 8.28571 18.75 8.28571C19.8546 8.28571 20.75 7.39028 20.75 6.28571C20.75 5.18114 19.8546 4.28571 18.75 4.28571Z"/>
            </svg>
        ),
    },
    {
        label: __('Telegram', 'enable-button-icons'),
        value: 'telegram',
        icon: (
            <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M9.66718 15.4667L9.27018 21.0507C9.83818 21.0507 10.0842 20.8067 10.3792 20.5137L13.0422 17.9687L18.5602 22.0097C19.5722 22.5737 20.2852 22.2767 20.5582 21.0787L24.1802 4.10668L24.1812 4.10568C24.5022 2.60968 23.6402 2.02468 22.6542 2.39168L1.36418 10.5427C-0.0888222 11.1067 -0.0668222 11.9167 1.11718 12.2837L6.56018 13.9767L19.2032 6.06568C19.7982 5.67168 20.3392 5.88968 19.8942 6.28368L9.66718 15.4667Z"/>
            </svg>
        ),
    },
    {
        label: __('Whatsapp', 'enable-button-icons'),
        value: 'whatsapp',
        icon: (
            <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M12.8375 0.285706C6.37563 0.285706 1.11852 5.54281 1.11852 12.0046C1.11852 14.2119 1.7308 16.3479 2.89304 18.2069L0.990174 23.2918C0.889132 23.562 0.955071 23.8662 1.15899 24.0702C1.29955 24.2106 1.48766 24.2857 1.67951 24.2857C1.76605 24.2857 1.85333 24.2705 1.9373 24.239L7.18896 22.2738C8.91271 23.2233 10.8578 23.7235 12.8374 23.7235C19.2993 23.7235 24.5564 18.4664 24.5564 12.0046C24.5564 5.54281 19.2994 0.285706 12.8375 0.285706ZM18.4844 15.6847C18.1572 16.8484 16.9339 17.6928 15.5757 17.6928C15.4429 17.6928 15.3087 17.6845 15.179 17.6684C13.5282 17.4311 11.5963 16.2985 10.0074 14.6349C8.33413 13.0368 7.20155 11.1052 6.97761 9.46769C6.75095 7.8104 7.49253 6.5745 8.96113 6.1615C9.03656 6.14031 9.11634 6.12956 9.19824 6.12956C10.0585 6.12956 11.0833 7.3453 11.4393 8.21685C11.6711 8.78446 11.6535 9.22175 11.3885 9.4816C10.7235 10.1333 10.4757 10.5295 10.5043 10.8949C10.5364 11.3021 10.9154 11.7726 11.5592 12.4476C11.6413 12.5338 11.7513 12.6486 11.8748 12.7726C11.9901 12.8875 12.1006 12.9934 12.1984 13.0869C12.9171 13.7723 13.3865 14.1438 13.8082 14.1438C14.1588 14.1437 14.539 13.8952 15.1641 13.2574C15.2588 13.1608 15.4335 13.0458 15.7277 13.0458C16.5035 13.0458 17.5983 13.8029 18.1341 14.5101C18.4589 14.9385 18.58 15.3447 18.4844 15.6847Z"/>
            </svg>
        ),
    },
    {
        label: __('Igaming Hub', 'enable-button-icons'),
        value: 'igaming-hub',
        icon: (
            <svg width="25" height="25" viewBox="0 0 25 25"  xmlns="http://www.w3.org/2000/svg">
                <path d="M15.6107 10.5695C15.4659 6.44543 9.53646 6.44715 9.39258 10.5696C9.52615 14.6886 15.478 14.6869 15.6107 10.5695Z" />
                <path d="M3.4315 11.7729C3.76932 12.0375 4.16534 12.2174 4.58657 12.2975C5.00781 12.3777 5.44205 12.3559 5.85315 12.2338C6.26425 12.1117 6.6403 11.893 6.94998 11.5959C7.25965 11.2987 7.49399 10.9317 7.63348 10.5254C7.77296 10.1191 7.81356 9.68527 7.75188 9.26007C7.69019 8.83487 7.52803 8.43059 7.27888 8.08091C7.02973 7.73123 6.70081 7.44627 6.31952 7.24975C5.93823 7.05324 5.5156 6.95087 5.08684 6.95117C4.53031 6.95898 3.98974 7.13872 3.53894 7.46585C3.08813 7.79297 2.74905 8.25155 2.56798 8.77897C2.38691 9.3064 2.37267 9.87697 2.52722 10.4128C2.68177 10.9486 2.99756 11.4236 3.4315 11.7729Z" />
                <path d="M6.28414 16.8869C6.29228 15.6192 7.16063 14.5311 8.01664 13.7067C8.24869 13.4932 8.49671 13.2979 8.75852 13.1223C8.42087 12.7457 8.02571 12.4251 7.58779 12.1726C6.92322 12.8359 6.02349 13.2083 5.08555 13.2083C4.1476 13.2083 3.24787 12.8358 2.58331 12.1725C1.38855 12.8937 -0.117609 14.4888 0.765034 15.9715C0.930056 16.2514 1.16532 16.4832 1.44745 16.6438C1.72957 16.8044 2.04872 16.8882 2.37318 16.8869H6.28414Z" />
                <path d="M17.2236 9.64998C17.3412 13.2153 22.4929 13.2139 22.6098 9.64992C22.4818 6.07882 17.3513 6.07903 17.2236 9.64998Z" />
                <path d="M15.7227 13.8099C15.5506 13.6932 15.3716 13.587 15.1867 13.4919C15.062 13.6075 14.9303 13.7152 14.7921 13.8142C14.0507 14.3372 13.153 14.5903 12.2482 14.5317C11.3434 14.4731 10.4857 14.1061 9.81764 13.4918C8.5704 14.1766 7.26213 15.3816 7.14163 16.8868C7.13882 17.4595 7.36315 18.0099 7.76528 18.4169C8.1674 18.8239 8.71439 19.0541 9.28592 19.057L15.7184 19.057C16.0885 19.0525 16.4513 18.9529 16.772 18.7676C17.0926 18.5823 17.3605 18.3177 17.5498 17.999C17.7392 17.6802 17.8437 17.3181 17.8535 16.9473C17.8632 16.5765 17.7777 16.2094 17.6053 15.8812C17.1598 15.0443 16.5127 14.3323 15.7227 13.8099Z" />
                <path d="M24.2819 14.1063C23.8499 13.302 23.2062 12.6316 22.4207 12.1682C21.7566 12.8334 20.8562 13.2074 19.9172 13.2082C18.9782 13.209 18.0773 12.8365 17.412 12.1725C16.9758 12.4275 16.581 12.7478 16.2412 13.1222C16.5031 13.2977 16.7512 13.4931 16.9831 13.7067C17.5898 14.252 18.0849 14.9103 18.4411 15.6448C18.6224 16.0338 18.7175 16.4575 18.7199 16.8868H22.6309C22.9549 16.8828 23.2724 16.7955 23.5531 16.6333C23.8338 16.471 24.0682 16.2393 24.2339 15.9602C24.3996 15.6812 24.491 15.3642 24.4994 15.0396C24.5078 14.7149 24.4329 14.3936 24.2819 14.1063Z"/>
                <path d="M22.8555 18.2286C22.8062 18.2012 22.752 18.1839 22.696 18.1776C22.6401 18.1713 22.5834 18.1761 22.5293 18.1917C22.4752 18.2073 22.4247 18.2335 22.3806 18.2687C22.3366 18.3039 22.2999 18.3474 22.2727 18.3969C18.1265 26.0596 6.88356 26.0577 2.73895 18.3967C2.71172 18.3473 2.67504 18.3037 2.63101 18.2685C2.58698 18.2333 2.53646 18.2072 2.48234 18.1915C2.42822 18.1759 2.37156 18.1711 2.31558 18.1774C2.25961 18.1837 2.20542 18.201 2.15611 18.2283C2.10681 18.2556 2.06335 18.2923 2.02822 18.3364C1.99309 18.3806 1.96697 18.4312 1.95136 18.4854C1.93576 18.5396 1.93096 18.5964 1.93725 18.6525C1.94355 18.7086 1.9608 18.7629 1.98804 18.8123C6.45244 27.0636 18.5608 27.0617 23.0236 18.8122C23.0508 18.7628 23.0681 18.7085 23.0744 18.6525C23.0806 18.5964 23.0758 18.5397 23.0602 18.4855C23.0446 18.4313 23.0185 18.3807 22.9834 18.3366C22.9482 18.2925 22.9048 18.2558 22.8555 18.2286Z" />
                <path d="M4.04848 5.73289C8.30859 0.565361 16.7027 0.565907 20.9622 5.733C21.0369 5.81699 21.1414 5.8683 21.2533 5.87595C21.3653 5.88359 21.4758 5.84695 21.5611 5.77389C21.6465 5.70083 21.6998 5.59717 21.7097 5.48514C21.7197 5.37312 21.6854 5.26165 21.6143 5.17466C17.0259 -0.391786 7.98426 -0.391379 3.39641 5.17483C3.32506 5.26179 3.29062 5.37334 3.30049 5.48548C3.31035 5.59763 3.36374 5.70142 3.44917 5.77453C3.53459 5.84764 3.64524 5.88424 3.75731 5.87645C3.86939 5.86866 3.97393 5.81711 4.04848 5.73289Z" />
                <path d="M17.8574 6.18012C17.9467 6.1785 18.0334 6.1491 18.1054 6.09599C18.1774 6.04289 18.2311 5.96869 18.2592 5.88365C18.2873 5.79862 18.2884 5.70693 18.2623 5.62126C18.2362 5.53559 18.1842 5.46016 18.1134 5.40539C16.4892 4.20488 14.5239 3.55718 12.5057 3.55719C10.4874 3.5572 8.52219 4.20491 6.89793 5.40544C6.8091 5.47429 6.75077 5.57531 6.73545 5.6868C6.72014 5.79828 6.74907 5.91134 6.81604 6.00168C6.883 6.09202 6.98265 6.15244 7.09359 6.16995C7.20452 6.18746 7.31788 6.16067 7.40931 6.09534C8.88557 5.00454 10.6716 4.41606 12.5057 4.41607C14.3399 4.41608 16.1258 5.00459 17.6021 6.0954C17.6758 6.15049 17.7654 6.18021 17.8574 6.18012Z"/>
                <path d="M5.78717 18.3376C5.71565 18.2509 5.61307 18.1957 5.50144 18.1839C5.3898 18.1721 5.278 18.2046 5.19001 18.2745C5.10201 18.3443 5.04484 18.446 5.03074 18.5576C5.01665 18.6692 5.04675 18.7819 5.1146 18.8715C8.73285 23.625 16.2788 23.6246 19.8966 18.8713C19.9645 18.7817 19.9947 18.669 19.9806 18.5573C19.9665 18.4457 19.9093 18.344 19.8213 18.2741C19.7332 18.2043 19.6214 18.1718 19.5097 18.1837C19.398 18.1955 19.2954 18.2508 19.224 18.3376C15.9345 22.6598 9.0762 22.6593 5.78717 18.3376Z" />
            </svg>
        ),
    },
];

/**
 * Add the attributes needed for button icons.
 *
 * @since 0.1.0
 * @param {Object} settings
 */
function addAttributes(settings) {
    if ('core/button' !== settings.name) {
        return settings;
    }

    // Add the block visibility attributes.
    const iconAttributes = {
        icon: {
            type: 'string',
        },
        iconPositionLeft: {
            type: 'boolean',
            default: false,
        },
        isOnlyIcon: {
            type: 'boolean',
            default: false,
        },
        iconHeight: {
            type: 'string',
            default: '1em',
        },
    };

    const newSettings = {
        ...settings,
        attributes: {
            ...settings.attributes,
            ...iconAttributes,
        },
    };

    return newSettings;
}

addFilter(
    'blocks.registerBlockType',
    'enable-button-icons/add-attributes',
    addAttributes
);

/**
 * Filter the BlockEdit object and add icon inspector controls to button blocks.
 *
 * @since 0.1.0
 * @param {Object} BlockEdit
 */
function addInspectorControls(BlockEdit) {
    return (props) => {
        if (props.name !== 'core/button') {
            return <BlockEdit {...props} />;
        }

        const {attributes, setAttributes} = props;
        const {icon: currentIcon, iconPositionLeft, isOnlyIcon, iconHeight} = attributes;

        return (
            <>
                <BlockEdit {...props} />
                <InspectorControls>
                    <PanelBody
                        title={__('Icon settings', 'enable-button-icons')}
                        className="button-icon-picker"
                        initialOpen={true}
                    >
                        <PanelRow>
                            <Grid
                                className="button-icon-picker__grid"
                                columns="5"
                                gap="1"
                            >
                                {ICONS.map((icon, index) => (
                                    <Button
                                        key={index}
                                        label={icon?.label}
                                        isPressed={currentIcon === icon.value}
                                        className="button-icon-picker__button"
                                        onClick={() =>
                                            setAttributes({
                                                // Allow user to disable icons.
                                                icon:
                                                    currentIcon === icon.value
                                                        ? null
                                                        : icon.value,
                                            })
                                        }
                                    >
                                        {icon.icon ?? icon.value}
                                    </Button>
                                ))}
                            </Grid>
                        </PanelRow>
                        <PanelRow>
                            <UnitControl
                                label={__('Icon height', 'legendsblocks')}
                                value={iconHeight}
                                onChange={(next) => setAttributes({iconHeight: next || ''})}
                                units={[
                                    {value: 'px', label: 'px'},
                                    {value: 'em', label: 'em'},
                                    {value: 'rem', label: 'rem'},
                                ]}
                                help={__('Set only the height; width will follow the SVG viewBox.', 'legendsblocks')}
                                min={0}
                            />

                        </PanelRow>
                        <PanelRow>
                            <ToggleControl
                                label={__(
                                    'Show icon on left',
                                    'enable-button-icons'
                                )}
                                checked={iconPositionLeft}
                                onChange={() => {
                                    setAttributes({
                                        iconPositionLeft: !iconPositionLeft,
                                    });
                                }}
                            />
                        </PanelRow>
                        <ToggleControl
                            label={__(
                                'Show only Icon',
                                'enable-button-icons'
                            )}
                            checked={isOnlyIcon}
                            onChange={() => {
                                setAttributes({
                                    isOnlyIcon: !isOnlyIcon,
                                });
                            }}
                        />
                    </PanelBody>
                </InspectorControls>
            </>
        );
    };
}

addFilter(
    'editor.BlockEdit',
    'enable-button-icons/add-inspector-controls',
    addInspectorControls
);

/**
 * Add icon and position classes in the Editor.
 *
 * @since 0.1.0
 * @param {Object} BlockListBlock
 */
function setAttributes(BlockListBlock) {
    return (props) => {
        const {name, attributes} = props;

        if ('core/button' !== name || !attributes?.icon) {
            return <BlockListBlock {...props} />;
        }

        const classes = classnames(props?.className, {
            [`has-icon__${attributes?.icon}`]: attributes?.icon,
            'has-icon-position__left': attributes?.iconPositionLeft,
            'has-icon-only': attributes?.isOnlyIcon,
        });
        if (attributes?.iconHeight) {
            const wrapperProps = {
                style: {
                    'height': attributes?.iconHeight,
                }
            };
            return <BlockListBlock {...props} className={classes} wrapperProps={wrapperProps}/>;
        }

        return <BlockListBlock {...props} className={classes}/>;
    };
}

addFilter(
    'editor.BlockListBlock',
    'enable-button-icons/add-classes',
    setAttributes
);
