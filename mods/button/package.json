{"name": "enable-button-icons", "version": "0.1.0", "description": "Add icons to Button blocks.", "author": "<PERSON>", "license": "GPL-2.0-or-later", "repository": {"type": "git", "url": "git://github.com/ndiego/enable-button-icons.git"}, "scripts": {"build": "wp-scripts build", "lint:css": "wp-scripts lint-style", "lint:js": "wp-scripts lint-js", "lint:js:src": "wp-scripts lint-js ./src", "lint:js:src:fix": "wp-scripts lint-js ./src --fix", "start": "wp-scripts start", "packages-update": "wp-scripts packages-update", "update-pot": "wp i18n make-pot . languages/enable-button-icons.pot --exclude=src"}, "dependencies": {"classnames": "^2.3.2"}, "devDependencies": {"@wordpress/icons": "^9.35.0", "@wordpress/scripts": "^26.15.0", "webpack-remove-empty-scripts": "^0.8.4"}}