<?php
/**
 * Plugin Name:       Brand Legends Blocks
 * Description:       Gutenberg blocks created by Brandlegends.com.
 * Requires at least: 6.1
 * Requires PHP:      7.0
 * Version:           0.1.1
 * Author:            The WordPress Contributors
 * License:           GPL-2.0-or-later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       legendsblocks
 *
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * Registers the block using the metadata loaded from the `block.json` file.
 * Behind the scenes, it registers also all assets so they can be enqueued
 * through the block editor in the corresponding context.
 *
 * @see https://developer.wordpress.org/reference/functions/register_block_type/
 */
function register_blocks() {
    if ( function_exists( 'pll_the_languages' ) ) {
        register_block_type(__DIR__ . '/blocks/polylang-switcher/build');
    }
    register_block_type(__DIR__ . '/blocks/faq-section/build');
    register_block_type(__DIR__ . '/blocks/slider/build');
    register_block_type(__DIR__ . '/blocks/popup/build');
}
add_action( 'init', 'register_blocks' );

/**
 * Enqueue Editor scripts and styles.
 */
function enable_button_icons_enqueue_block_editor_assets() {
    $plugin_path = untrailingslashit( plugin_dir_path( __FILE__ ) );
    $plugin_url  = untrailingslashit( plugin_dir_url( __FILE__ ) );
    $asset_file  = include untrailingslashit( plugin_dir_path( __FILE__ ) ) . '/mods/button/build/index.asset.php';

    wp_enqueue_script(
        'enable-button-icons-editor-scripts',
        $plugin_url . '/mods/button/build/index.js',
        $asset_file['dependencies'],
        $asset_file['version']
    );

    wp_set_script_translations(
        'enable-button-icons-editor-scripts',
        'enable-button-icons',
        $plugin_path . '/languages'
    );

    wp_enqueue_style(
        'enable-button-icons-editor-styles',
        $plugin_url . '/mods/button/build/editor.css'
    );
}
add_action( 'enqueue_block_editor_assets', 'enable_button_icons_enqueue_block_editor_assets' );
/**
 * Enqueue Editor scripts and styles.
 */
function enable_details_icons_enqueue_block_editor_assets() {
    $plugin_path = untrailingslashit( plugin_dir_path( __FILE__ ) );
    $plugin_url  = untrailingslashit( plugin_dir_url( __FILE__ ) );

    wp_set_script_translations(
        'enable-details-icons-editor-scripts',
        'enable-details-icons',
        $plugin_path . '/languages'
    );

}
add_action( 'enqueue_block_editor_assets', 'enable_details_icons_enqueue_block_editor_assets' );

/**
 * Enqueue block styles
 * (Applies to both frontend and Editor)
 */
function enable_block_modifications_block_styles() {
    $plugin_path = untrailingslashit( plugin_dir_path( __FILE__ ) );
    $plugin_url  = untrailingslashit( plugin_dir_url( __FILE__ ) );

    wp_enqueue_block_style(
        'core/button',
        array(
            'handle' => 'enable-button-icons-block-styles',
            'src'    => $plugin_url . '/mods/button/build/style.css',
            'ver'    => wp_get_theme()->get( 'Version' ),
            'path'   => $plugin_path . '/mods/button/build/style.css',
        )
    );


}
add_action( 'init', 'enable_block_modifications_block_styles' );

/**
 * Render icons on the frontend.
 */
function enable_button_icons_render_block_button( $block_content, $block ) {
    print('_______________');
    print_r( $block['innerHTML']);
    print('_______________');


    if ( empty( $block['blockName'] ) || 'core/button' !== $block['blockName'] ) {
        return $block_content;
    }
    if ( empty( $block['attrs']['icon'] ) ) {
        return $block_content;
    }


    $attrs          = $block['attrs'];
    $icon           = $attrs['icon'];
    $position_left  = ! empty( $attrs['iconPositionLeft'] );
    $is_only_icon   = ! empty( $attrs['isOnlyIcon'] );
    $icon_height    = ! empty( $attrs['iconHeight'] ) ? $attrs['iconHeight'] : '1em';

//    if ( $is_only_icon ) {
//       print_r( $attrs);
//    }

    // All available icon SVGs.
    $icons = array(
        'arrow-left'            => "<svg viewBox='0 0 16 11' xmlns='http://www.w3.org/2000/svg'><polygon points='16 4.7 2.88198758 4.7 6.55900621 1 5.56521739 0 0 5.5 5.56521739 11 6.55900621 10 2.88198758 6.3 16 6.3'></polygon></svg>",
        'arrow-right'           => "<svg viewBox='0 0 16 11' xmlns='http://www.w3.org/2000/svg'><polygon points='0 4.7 13.1180124 4.7 9.44099379 1 10.4347826 0 16 5.5 10.4347826 11 9.44099379 10 13.1180124 6.3 0 6.3'></polygon></svg>",
        'chevron-left'          => "<svg viewBox='0 0 10 18' xmlns='http://www.w3.org/2000/svg'><polygon points='8.18181818 0 10 1.5 3.03030303 9 10 16.5 8.18181818 18 0 9'></polygon></svg>",
        'chevron-left-small'    => "<svg viewBox='0 0 7 12' xmlns='http://www.w3.org/2000/svg'><polygon points='5.25057985 0 0 5.99997743 5.25057985 12 7 10.5313252 3.03456266 5.99997743 7 1.46865977'></polygon></svg>",
        'chevron-right'         => "<svg viewBox='0 0 10 18' xmlns='http://www.w3.org/2000/svg'><polygon points='1.81818182 0 0 1.5 6.96969697 9 0 16.5 1.81818182 18 10 9'></polygon></svg>",
        'chevron-right-small'   => "<svg viewBox='0 0 7 12' xmlns='http://www.w3.org/2000/svg'><polygon points='1.74942015 0 7 5.99997743 1.74942015 12 0 10.5313252 3.96543734 5.99997743 0 1.46865977'></polygon></svg>",
        'cloud'                 => "<svg viewBox='0 0 16 11' xmlns='http://www.w3.org/2000/svg'><path d='M13.3787444,4.44036697 C13.3787444,1.91743119 11.2663111,0 8.55032538,0 C6.33729999,0 4.42605079,1.41284404 3.92309047,3.33027523 L3.72190634,3.33027523 C1.71006508,3.33027523 0,5.04587156 0,7.16513761 C0,9.28440367 1.71006508,11 3.72190634,11 L12.775192,11 C14.5858492,11 15.9941381,9.48623853 15.9941381,7.66972477 C16.0947301,6.05504587 14.8876254,4.74311927 13.3787444,4.44036697 L13.3787444,4.44036697 Z M12.8757841,9.58715596 L3.82249841,9.58715596 C2.61539365,9.58715596 1.60947301,8.47706422 1.60947301,7.26605505 C1.60947301,6.05504587 2.61539365,4.8440367 3.82249841,4.8440367 L5.13019523,4.8440367 L5.43197142,3.73394495 C5.83433967,2.42201835 7.1420365,1.51376147 8.65091745,1.51376147 C10.4615746,1.51376147 11.9704555,2.82568807 11.9704555,4.44036697 L11.9704555,5.75229358 L13.2781524,5.95412844 C14.0828889,6.05504587 14.6864412,6.86238532 14.6864412,7.7706422 C14.5858492,8.77981651 13.7811127,9.58715596 12.8757841,9.58715596 Z'></path></svg>",
        'cloud-upload'          => "<svg viewBox='0 0 16 11' xmlns='http://www.w3.org/2000/svg'><path d='M13.3,4.4 C13.3,1.9 11.2,-1.24344979e-14 8.5,-1.24344979e-14 C6.3,-1.24344979e-14 4.4,1.4 3.9,3.3 L3.7,3.3 C1.7,3.3 1.95399252e-14,5 1.95399252e-14,7.1 C1.95399252e-14,9.2 1.7,10.9 3.7,10.9 L12.7,10.9 C14.5,10.9 15.9,9.4 15.9,7.6 C16,6 14.8,4.7 13.3,4.4 L13.3,4.4 Z M12.8,9.5 L8.8,9.5 L8.8,7.1 L10,8.3 L11,7.3 L8,4.3 L5,7.3 L6,8.3 L7.2,7.1 L7.2,9.5 L3.7,9.5 C2.5,9.5 1.5,8.4 1.5,7.2 C1.5,6 2.5,4.8 3.7,4.8 L5,4.8 L5.3,3.7 C5.7,2.4 7,1.5 8.5,1.5 C10.3,1.5 11.8,2.8 11.8,4.4 L11.8,5.7 L13.1,5.9 C13.9,6 14.5,6.8 14.5,7.7 C14.5,8.7 13.7,9.5 12.8,9.5 L12.8,9.5 Z'></path></svg>",
        'comment-author-avatar' => "<svg viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'><path d='M8,0 C12.418278,0 16,3.581722 16,8 C16,12.418278 12.418278,16 8,16 C3.581722,16 0,12.418278 0,8 C0,3.581722 3.581722,0 8,0 Z M10,10.75 L6,10.75 C5.31,10.75 4.75,11.31 4.75,12 L4.75,13.63 C5.73765837,14.2015236 6.85890165,14.5016718 8,14.5000138 C9.14109835,14.5016718 10.2623416,14.2015236 11.25,13.63 L11.25,12 C11.25,11.31 10.69,10.75 10,10.75 Z M12.5437324,3.35187647 C10.0177861,0.882628059 5.9822139,0.882628059 3.45626762,3.35187647 C0.93032134,5.82112489 0.83872177,9.8556574 3.25,12.437 L3.25,12 C3.25,10.4812169 4.48121694,9.25 5.99999011,9.25 L9.99999011,9.25 C11.5187831,9.25 12.75,10.4812169 12.75,12 L12.75,12.437 C15.1612782,9.8556574 15.0696787,5.82112489 12.5437324,3.35187647 Z M8,4 C9.1045695,4 10,4.8954305 10,6 C10,7.1045695 9.1045695,8 8,8 C6.8954305,8 6,7.1045695 6,6 C6,4.8954305 6.8954305,4 8,4 Z'></path></svg>",
        'download'              => "<svg viewBox='0 0 16 17' xmlns='http://www.w3.org/2000/svg'><path d='M14,8.3 L13,7.2 L9,11.2 L9,0 L7.5,0 L7.5,11.3 L3,7.2 L2,8.3 L8.2,14.1 L14,8.3 L14,8.3 Z M14.5,12 L14.5,15.5 L1.5,15.5 L1.5,12 L0,12 L0,17 L16,17 L16,12 L14.5,12 Z'></path></svg>",
        'external'              => "<svg viewBox='0 0 15 15' xmlns='http://www.w3.org/2000/svg'><path d='M15,0 L8,0 L8,1.5 L12.44,1.5 L6.47,7.47 L7.53,8.53 L13.5,2.56 L13.5,7 L15,7 L15,0 Z M2,1 C0.8954305,1 0,1.8954305 0,3 L0,13 C0,14.1045695 0.8954305,15 2,15 L12,15 C13.1045695,15 14,14.1045695 14,13 L14,10 L12.5,10 L12.5,13 C12.5,13.2761424 12.2761424,13.5 12,13.5 L2,13.5 C1.72385763,13.5 1.5,13.2761424 1.5,13 L1.5,3 C1.5,2.72385763 1.72385763,2.5 2,2.5 L5,2.5 L5,1 L2,1 Z'></path></svg>",
        'external-arrow'        => "<svg viewBox='0 0 12 12' xmlns='http://www.w3.org/2000/svg'><polygon points='12 0 2.15240328 0 2.15240328 2.1101993 8.3985932 2.1101993 0 10.5087925 1.4912075 12 9.8898007 3.6014068 9.8898007 9.84759672 12 9.84759672'></polygon></svg>",
        'help'                  => "<svg viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'><path d='M8,1.37142857 C5.63183949,1.37142857 3.44356893,2.63482711 2.25948867,4.68571426 C1.07540841,6.73660141 1.07540841,9.26339859 2.25948867,11.3142857 C3.44356893,13.3651729 5.63183949,14.6285714 8,14.6285714 C11.6608589,14.6285714 14.6285713,11.6608589 14.6285713,8 C14.6285713,4.33914113 11.6608589,1.37142857 8,1.37142857 Z M0,8 C0,3.581722 3.581722,0 8,0 C12.418278,0 16,3.581722 16,8 C16,12.418278 12.418278,16 8,16 C3.581722,16 0,12.418278 0,8 Z M8,5.02857143 C8.72564752,5.03138642 9.32350762,5.59898692 9.36397433,6.32351069 C9.40444104,7.04803446 8.87350647,7.67868136 8.15268571,7.76228571 C7.72754286,7.80982857 7.31428571,8.16457143 7.31428571,8.68571429 L7.31428571,9.82857143 L8.68571429,9.82857143 L8.68571429,9.056 C10.0658081,8.69966131 10.9429771,7.34564321 10.704046,5.94045706 C10.465115,4.53527091 9.18971942,3.5472645 7.76941052,3.66709276 C6.34910163,3.78692102 5.25726757,4.97464525 5.25714286,6.4 L6.62857143,6.4 C6.62857143,5.64258091 7.24258091,5.02857143 8,5.02857143 Z M7.31428571,10.7428571 L7.31428571,12.1142857 L8.68571429,12.1142857 L8.68571429,10.7428571 L7.31428571,10.7428571 Z'></path></svg>",
        'info'                  => "<svg viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'><path d='M8,0 C3.63636364,0 0,3.54545455 0,8 C0,12.3636364 3.54545455,16 8,16 C12.3636364,16 16,12.4545455 16,8 C16,3.63636364 12.3636364,0 8,0 L8,0 Z M8,14.5454545 C4.36363636,14.5454545 1.45454545,11.5454545 1.45454545,8 C1.45454545,4.36363636 4.36363636,1.45454545 8,1.45454545 C11.6363636,1.45454545 14.5454545,4.45454545 14.5454545,8 C14.5454545,11.6363636 11.6363636,14.5454545 8,14.5454545 Z M7.2,12.5454545 L8.8,12.5454545 L8.8,7.09090909 L7.2,7.09090909 L7.2,12.5454545 Z M7.2,5.05454545 L8.8,5.05454545 L8.8,3.45454545 L7.2,3.45454545 L7.2,5.05454545 Z'></path></svg>",
        'lock-outline'          => "<svg viewBox='0 0 12 17' xmlns='http://www.w3.org/2000/svg'><path d='M11,6.88095238 L9.8,6.88095238 L9.8,3.8452381 C9.8,1.7202381 8.1,0 6,0 C3.9,0 2.2,1.7202381 2.2,3.8452381 L2.2,6.88095238 L1,6.88095238 C0.4,6.88095238 0,7.28571429 0,7.89285714 L0,15.9880952 C0,16.5952381 0.4,17 1,17 L11,17 C11.6,17 12,16.5952381 12,15.9880952 L12,7.89285714 C12,7.28571429 11.6,6.88095238 11,6.88095238 Z M3.8,3.8452381 C3.8,2.63095238 4.8,1.61904762 6,1.61904762 C7.2,1.61904762 8.2,2.63095238 8.2,3.8452381 L8.2,6.88095238 L3.8,6.88095238 L3.8,3.8452381 Z M10.5,15.4821429 L1.5,15.4821429 L1.5,8.39880952 L10.5,8.39880952 L10.5,15.4821429 Z'></path></svg>",
        'login'                 => "<svg viewBox='0 0 14 14' xmlns='http://www.w3.org/2000/svg'><path d='M6.08695652,9.5 L7.20289855,10.6 L10.2463768,7.6 L10.7536232,7.1 L10.1449275,6.5 L7.10144928,3.5 L6.08695652,4.5 L7.8115942,6.2 L0,6.2 L0,7.7 L7.8115942,7.7 L6.08695652,9.5 Z M11.9710145,0 L4.86956522,0 C3.75362319,0 2.84057971,0.9 2.84057971,2 L2.84057971,3.5 L4.36231884,3.5 L4.36231884,2 C4.36231884,1.7 4.56521739,1.5 4.86956522,1.5 L11.9710145,1.5 C12.2753623,1.5 12.4782609,1.7 12.4782609,2 L12.4782609,12 C12.4782609,12.3 12.2753623,12.5 11.9710145,12.5 L4.86956522,12.5 C4.56521739,12.5 4.36231884,12.3 4.36231884,12 L4.36231884,10.5 L2.84057971,10.5 L2.84057971,12 C2.84057971,13.1 3.75362319,14 4.86956522,14 L11.9710145,14 C13.0869565,14 14,13.1 14,12 L14,2 C14,0.9 13.0869565,0 11.9710145,0 Z'></path></svg>",
        'next'                  => "<svg viewBox='0 0 13 12' xmlns='http://www.w3.org/2000/svg'><path d='M1.2,0 L2.22044605e-14,1 L4.5,6 L2.22044605e-14,11 L1.1,12 L6.6,6 L1.2,0 Z M7.2,0 L6.1,1 L10.6,6 L6.1,11 L7.2,12 L12.7,6 L7.2,0 L7.2,0 Z'></path></svg>",
        'previous'              => "<svg viewBox='0 0 13 12' xmlns='http://www.w3.org/2000/svg'><path d='M11.5,0 L12.7,1 L8.2,6 L12.7,11 L11.6,12 L6.1,6 L11.5,0 Z M5.5,0 L6.6,1 L2.1,6 L6.6,11 L5.5,12 L2.30926389e-14,6 L5.5,0 Z'></path></svg>",
        'shuffle'               => "<svg viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'><path d='M13.1369889,2.76840729 L11.4221697,1.05557976 L12.4777494,0 L16,3.51527977 L12.4777494,7.03055953 L11.4221697,5.97497977 L13.1379847,4.26215224 L9.96128711,4.26215224 C9.36179747,4.26215224 8.97242796,4.46330989 8.68662476,4.75807556 C8.3789133,5.07574532 8.15086824,5.54677289 7.99452294,6.1353084 C7.86506504,6.62127342 7.77344868,7.15404245 7.69975727,7.63701998 C7.66589905,8.29825107 7.58424099,9.04412772 7.39802079,9.74519201 C7.20582561,10.4631854 6.88716002,11.2060746 6.33447439,11.7766851 C5.76187216,12.3682081 4.98114147,12.7267069 3.98730317,12.7267069 L0,12.7267069 L0,11.232962 L3.98630734,11.232962 C4.58480115,11.232962 4.97516649,11.0318043 5.26096969,10.7370387 C5.56768532,10.4193689 5.79672621,9.94834132 5.95307151,9.35980581 C6.10145018,8.80512852 6.16817078,8.27136366 6.23987054,7.70473642 L6.27870791,7.39602913 C6.32112368,6.84051422 6.41175346,6.28973838 6.54957366,5.7499222 C6.74176884,5.03093297 7.06043443,4.28903965 7.61212423,3.71842908 C8.18572229,3.12690608 8.96645298,2.76840729 9.96029128,2.76840729 L13.1369889,2.76840729 L13.1369889,2.76840729 Z M3.98730317,2.76840729 C4.78396714,2.76840729 5.44121491,2.99744819 5.96701313,3.39378851 C5.68290358,3.82697693 5.45407907,4.29399304 5.28586544,4.78396714 C5.27798753,4.77525513 5.27002047,4.76662415 5.26196552,4.75807556 C4.97616232,4.46330989 4.58579698,4.26215224 3.98730317,4.26215224 L0,4.26215224 L0,2.76840729 L3.98630734,2.76840729 L3.98730317,2.76840729 Z M8.66272484,10.7111471 C8.50239622,11.168233 8.28430945,11.6472272 7.98157715,12.1013257 C8.50637954,12.4966702 9.16462314,12.7267069 9.96128711,12.7267069 L13.1379847,12.7267069 L11.4231655,14.4395344 L12.4787453,15.4951142 L16,11.9798344 L12.4777494,8.46455468 L11.4221697,9.52013444 L13.1379847,11.232962 L9.96128711,11.232962 C9.36179747,11.232962 8.97242796,11.0318043 8.68662476,10.7370387 C8.67858038,10.7284802 8.67061341,10.7198493 8.66272484,10.7111471 L8.66272484,10.7111471 Z'></path></svg>",
        'wordpress'             => "<svg viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'><path d='M20,10 C20,4.49 15.51,0 10,0 C4.48,0 0,4.49 0,10 C0,15.52 4.48,20 10,20 C15.51,20 20,15.52 20,10 Z M7.78,15.37 L4.37,6.22 C4.92,6.2 5.54,6.14 5.54,6.14 C6.04,6.08 5.98,5.01 5.48,5.03 C5.48,5.03 4.03,5.14 3.11,5.14 C2.93,5.14 2.74,5.14 2.53,5.13 C4.12,2.69 6.87,1.11 10,1.11 C12.33,1.11 14.45,1.98 16.05,3.45 C15.37,3.34 14.4,3.84 14.4,5.03 C14.4,5.77 14.85,6.39 15.3,7.13 C15.65,7.74 15.85,8.49 15.85,9.59 C15.85,11.08 14.45,14.59 14.45,14.59 L11.42,6.22 C11.96,6.2 12.24,6.05 12.24,6.05 C12.74,6 12.68,4.8 12.18,4.83 C12.18,4.83 10.74,4.95 9.8,4.95 C8.93,4.95 7.47,4.83 7.47,4.83 C6.97,4.8 6.91,6.03 7.41,6.05 L8.33,6.13 L9.59,9.54 L7.78,15.37 Z M17.41,10 C17.65,9.36 18.15,8.13 17.84,5.75 C18.54,7.04 18.89,8.46 18.89,10 C18.89,13.29 17.16,16.24 14.49,17.78 C15.46,15.19 16.43,12.58 17.41,10 Z M6.1,18.09 C3.12,16.65 1.11,13.53 1.11,10 C1.11,8.7 1.34,7.52 1.83,6.41 C3.25,10.3 4.67,14.2 6.1,18.09 Z M10.13,11.46 L12.71,18.44 C11.85,18.73 10.95,18.89 10,18.89 C9.21,18.89 8.43,18.78 7.71,18.56 C8.52,16.18 9.33,13.82 10.13,11.46 L10.13,11.46 Z'></path></svg>",
        'arrow-big-right'       => '<svg width="44" height="24" viewBox="0 0 44 24" fill="transparent" xmlns="http://www.w3.org/2000/svg">
<path d="M2 12L42 12" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M35 5L42 12L35 19" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
',
        'arrow-bold-right'      => '<svg width="46" height="24" viewBox="0 0 46 24" fill="transparent" xmlns="http://www.w3.org/2000/svg"><path d="M5 12L41 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M34 5L41 12L34 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
        'arrow-short-right'     => '<svg width="24" height="25" viewBox="0 0 24 25" fill="transparent" xmlns="http://www.w3.org/2000/svg"><path class="arrow-tail" d="M5 12.5H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 5.5L19 12.5L12 19.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
        'linkedin' => '<svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg"><g><path d="M24.25 23.7547V14.8807C24.25 10.1257 21.712 7.91323 18.328 7.91323C15.5972 7.91323 14.3733 9.41548 13.6915 10.47V8.27698H8.5465C8.61475 9.72973 8.5465 23.7547 8.5465 23.7547H13.6915V15.111C13.6915 14.6497 13.7245 14.187 13.861 13.857C14.2323 12.9322 15.079 11.9752 16.4995 11.9752C18.3617 11.9752 19.1058 13.3942 19.1058 15.4747V23.7555L24.25 23.7547ZM3.12625 6.16423C4.9195 6.16423 6.03775 4.97473 6.03775 3.48898C6.00475 1.97098 4.92025 0.815979 3.16075 0.815979C1.40125 0.815979 0.25 1.97023 0.25 3.48898C0.25 4.97473 1.36675 6.16423 3.094 6.16423H3.12625ZM5.69875 23.7547V8.27698H0.5545V23.7547H5.69875Z"/></g></svg>',
        'instagram' =>'<svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 1.28571C4.21243 1.28571 1.75 3.74814 1.75 6.78571V17.7857C1.75 20.8233 4.21243 23.2857 7.25 23.2857H18.25C21.2876 23.2857 23.75 20.8233 23.75 17.7857V6.78571C23.75 3.74814 21.2876 1.28571 18.25 1.28571H7.25ZM12.75 8.28571C10.5409 8.28571 8.75 10.0766 8.75 12.2857C8.75 14.4948 10.5409 16.2857 12.75 16.2857C14.9591 16.2857 16.75 14.4948 16.75 12.2857C16.75 10.0766 14.9591 8.28571 12.75 8.28571ZM6.75 12.2857C6.75 8.972 9.43629 6.28571 12.75 6.28571C16.0637 6.28571 18.75 8.972 18.75 12.2857C18.75 15.5994 16.0637 18.2857 12.75 18.2857C9.43629 18.2857 6.75 15.5994 6.75 12.2857ZM18.75 4.28571C17.6454 4.28571 16.75 5.18114 16.75 6.28571C16.75 7.39028 17.6454 8.28571 18.75 8.28571C19.8546 8.28571 20.75 7.39028 20.75 6.28571C20.75 5.18114 19.8546 4.28571 18.75 4.28571Z" /></svg>',
        'telegram' => '<svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg"><path d="M9.66718 15.4667L9.27018 21.0507C9.83818 21.0507 10.0842 20.8067 10.3792 20.5137L13.0422 17.9687L18.5602 22.0097C19.5722 22.5737 20.2852 22.2767 20.5582 21.0787L24.1802 4.10668L24.1812 4.10568C24.5022 2.60968 23.6402 2.02468 22.6542 2.39168L1.36418 10.5427C-0.0888222 11.1067 -0.0668222 11.9167 1.11718 12.2837L6.56018 13.9767L19.2032 6.06568C19.7982 5.67168 20.3392 5.88968 19.8942 6.28368L9.66718 15.4667Z" /></svg>',
        'whatsapp' => '<svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg"><path d="M12.8375 0.285706C6.37563 0.285706 1.11852 5.54281 1.11852 12.0046C1.11852 14.2119 1.7308 16.3479 2.89304 18.2069L0.990174 23.2918C0.889132 23.562 0.955071 23.8662 1.15899 24.0702C1.29955 24.2106 1.48766 24.2857 1.67951 24.2857C1.76605 24.2857 1.85333 24.2705 1.9373 24.239L7.18896 22.2738C8.91271 23.2233 10.8578 23.7235 12.8374 23.7235C19.2993 23.7235 24.5564 18.4664 24.5564 12.0046C24.5564 5.54281 19.2994 0.285706 12.8375 0.285706ZM18.4844 15.6847C18.1572 16.8484 16.9339 17.6928 15.5757 17.6928C15.4429 17.6928 15.3087 17.6845 15.179 17.6684C13.5282 17.4311 11.5963 16.2985 10.0074 14.6349C8.33413 13.0368 7.20155 11.1052 6.97761 9.46769C6.75095 7.8104 7.49253 6.5745 8.96113 6.1615C9.03656 6.14031 9.11634 6.12956 9.19824 6.12956C10.0585 6.12956 11.0833 7.3453 11.4393 8.21685C11.6711 8.78446 11.6535 9.22175 11.3885 9.4816C10.7235 10.1333 10.4757 10.5295 10.5043 10.8949C10.5364 11.3021 10.9154 11.7726 11.5592 12.4476C11.6413 12.5338 11.7513 12.6486 11.8748 12.7726C11.9901 12.8875 12.1006 12.9934 12.1984 13.0869C12.9171 13.7723 13.3865 14.1438 13.8082 14.1438C14.1588 14.1437 14.539 13.8952 15.1641 13.2574C15.2588 13.1608 15.4335 13.0458 15.7277 13.0458C16.5035 13.0458 17.5983 13.8029 18.1341 14.5101C18.4589 14.9385 18.58 15.3447 18.4844 15.6847Z" /></svg>',
       'igaming-hub' => '<svg width="25" height="25" viewBox="0 0 25 25"  xmlns="http://www.w3.org/2000/svg">
<path d="M15.6107 10.5695C15.4659 6.44543 9.53646 6.44715 9.39258 10.5696C9.52615 14.6886 15.478 14.6869 15.6107 10.5695Z" />
<path d="M3.4315 11.7729C3.76932 12.0375 4.16534 12.2174 4.58657 12.2975C5.00781 12.3777 5.44205 12.3559 5.85315 12.2338C6.26425 12.1117 6.6403 11.893 6.94998 11.5959C7.25965 11.2987 7.49399 10.9317 7.63348 10.5254C7.77296 10.1191 7.81356 9.68527 7.75188 9.26007C7.69019 8.83487 7.52803 8.43059 7.27888 8.08091C7.02973 7.73123 6.70081 7.44627 6.31952 7.24975C5.93823 7.05324 5.5156 6.95087 5.08684 6.95117C4.53031 6.95898 3.98974 7.13872 3.53894 7.46585C3.08813 7.79297 2.74905 8.25155 2.56798 8.77897C2.38691 9.3064 2.37267 9.87697 2.52722 10.4128C2.68177 10.9486 2.99756 11.4236 3.4315 11.7729Z" />
<path d="M6.28414 16.8869C6.29228 15.6192 7.16063 14.5311 8.01664 13.7067C8.24869 13.4932 8.49671 13.2979 8.75852 13.1223C8.42087 12.7457 8.02571 12.4251 7.58779 12.1726C6.92322 12.8359 6.02349 13.2083 5.08555 13.2083C4.1476 13.2083 3.24787 12.8358 2.58331 12.1725C1.38855 12.8937 -0.117609 14.4888 0.765034 15.9715C0.930056 16.2514 1.16532 16.4832 1.44745 16.6438C1.72957 16.8044 2.04872 16.8882 2.37318 16.8869H6.28414Z" />
<path d="M17.2236 9.64998C17.3412 13.2153 22.4929 13.2139 22.6098 9.64992C22.4818 6.07882 17.3513 6.07903 17.2236 9.64998Z" />
<path d="M15.7227 13.8099C15.5506 13.6932 15.3716 13.587 15.1867 13.4919C15.062 13.6075 14.9303 13.7152 14.7921 13.8142C14.0507 14.3372 13.153 14.5903 12.2482 14.5317C11.3434 14.4731 10.4857 14.1061 9.81764 13.4918C8.5704 14.1766 7.26213 15.3816 7.14163 16.8868C7.13882 17.4595 7.36315 18.0099 7.76528 18.4169C8.1674 18.8239 8.71439 19.0541 9.28592 19.057L15.7184 19.057C16.0885 19.0525 16.4513 18.9529 16.772 18.7676C17.0926 18.5823 17.3605 18.3177 17.5498 17.999C17.7392 17.6802 17.8437 17.3181 17.8535 16.9473C17.8632 16.5765 17.7777 16.2094 17.6053 15.8812C17.1598 15.0443 16.5127 14.3323 15.7227 13.8099Z" />
<path d="M24.2819 14.1063C23.8499 13.302 23.2062 12.6316 22.4207 12.1682C21.7566 12.8334 20.8562 13.2074 19.9172 13.2082C18.9782 13.209 18.0773 12.8365 17.412 12.1725C16.9758 12.4275 16.581 12.7478 16.2412 13.1222C16.5031 13.2977 16.7512 13.4931 16.9831 13.7067C17.5898 14.252 18.0849 14.9103 18.4411 15.6448C18.6224 16.0338 18.7175 16.4575 18.7199 16.8868H22.6309C22.9549 16.8828 23.2724 16.7955 23.5531 16.6333C23.8338 16.471 24.0682 16.2393 24.2339 15.9602C24.3996 15.6812 24.491 15.3642 24.4994 15.0396C24.5078 14.7149 24.4329 14.3936 24.2819 14.1063Z"/>
<path d="M22.8555 18.2286C22.8062 18.2012 22.752 18.1839 22.696 18.1776C22.6401 18.1713 22.5834 18.1761 22.5293 18.1917C22.4752 18.2073 22.4247 18.2335 22.3806 18.2687C22.3366 18.3039 22.2999 18.3474 22.2727 18.3969C18.1265 26.0596 6.88356 26.0577 2.73895 18.3967C2.71172 18.3473 2.67504 18.3037 2.63101 18.2685C2.58698 18.2333 2.53646 18.2072 2.48234 18.1915C2.42822 18.1759 2.37156 18.1711 2.31558 18.1774C2.25961 18.1837 2.20542 18.201 2.15611 18.2283C2.10681 18.2556 2.06335 18.2923 2.02822 18.3364C1.99309 18.3806 1.96697 18.4312 1.95136 18.4854C1.93576 18.5396 1.93096 18.5964 1.93725 18.6525C1.94355 18.7086 1.9608 18.7629 1.98804 18.8123C6.45244 27.0636 18.5608 27.0617 23.0236 18.8122C23.0508 18.7628 23.0681 18.7085 23.0744 18.6525C23.0806 18.5964 23.0758 18.5397 23.0602 18.4855C23.0446 18.4313 23.0185 18.3807 22.9834 18.3366C22.9482 18.2925 22.9048 18.2558 22.8555 18.2286Z" />
<path d="M4.04848 5.73289C8.30859 0.565361 16.7027 0.565907 20.9622 5.733C21.0369 5.81699 21.1414 5.8683 21.2533 5.87595C21.3653 5.88359 21.4758 5.84695 21.5611 5.77389C21.6465 5.70083 21.6998 5.59717 21.7097 5.48514C21.7197 5.37312 21.6854 5.26165 21.6143 5.17466C17.0259 -0.391786 7.98426 -0.391379 3.39641 5.17483C3.32506 5.26179 3.29062 5.37334 3.30049 5.48548C3.31035 5.59763 3.36374 5.70142 3.44917 5.77453C3.53459 5.84764 3.64524 5.88424 3.75731 5.87645C3.86939 5.86866 3.97393 5.81711 4.04848 5.73289Z" />
<path d="M17.8574 6.18012C17.9467 6.1785 18.0334 6.1491 18.1054 6.09599C18.1774 6.04289 18.2311 5.96869 18.2592 5.88365C18.2873 5.79862 18.2884 5.70693 18.2623 5.62126C18.2362 5.53559 18.1842 5.46016 18.1134 5.40539C16.4892 4.20488 14.5239 3.55718 12.5057 3.55719C10.4874 3.5572 8.52219 4.20491 6.89793 5.40544C6.8091 5.47429 6.75077 5.57531 6.73545 5.6868C6.72014 5.79828 6.74907 5.91134 6.81604 6.00168C6.883 6.09202 6.98265 6.15244 7.09359 6.16995C7.20452 6.18746 7.31788 6.16067 7.40931 6.09534C8.88557 5.00454 10.6716 4.41606 12.5057 4.41607C14.3399 4.41608 16.1258 5.00459 17.6021 6.0954C17.6758 6.15049 17.7654 6.18021 17.8574 6.18012Z"/>
<path d="M5.78717 18.3376C5.71565 18.2509 5.61307 18.1957 5.50144 18.1839C5.3898 18.1721 5.278 18.2046 5.19001 18.2745C5.10201 18.3443 5.04484 18.446 5.03074 18.5576C5.01665 18.6692 5.04675 18.7819 5.1146 18.8715C8.73285 23.625 16.2788 23.6246 19.8966 18.8713C19.9645 18.7817 19.9947 18.669 19.9806 18.5573C19.9665 18.4457 19.9093 18.344 19.8213 18.2741C19.7332 18.2043 19.6214 18.1718 19.5097 18.1837C19.398 18.1955 19.2954 18.2508 19.224 18.3376C15.9345 22.6598 9.0762 22.6593 5.78717 18.3376Z" />
</svg>');

    // Make sure the selected icon is in the array, otherwise bail.
    $icon_svg = isset( $icons[ $icon ] ) ? $icons[ $icon ] : '';
    if ( '' === trim( $icon_svg ) ) {
        return $block_content;
    }

// If the button has no text saved, build a valid icon-only button.
    // This avoids Core treating it as empty on the frontend.
    $content_is_empty = '' === trim( wp_strip_all_tags( $block_content ) );

    if ( $is_only_icon && $content_is_empty ) {

        // Read relevant anchor attrs from block attributes.
        $url         = isset( $attrs['url'] ) ? $attrs['url'] : '';
        // Temporary debug - remove after testing
        $rel         = isset( $attrs['rel'] ) ? $attrs['rel'] : '';
        // Different Core versions use `target` or `linkTarget`; support both.
        $target_attr = ! empty( $attrs['target'] ) ? $attrs['target'] : ( ! empty( $attrs['linkTarget'] ) ? $attrs['linkTarget'] : '' );
        $aria_label  = isset( $attrs['ariaLabel'] ) ? $attrs['ariaLabel'] : '';

        // Outer wrapper uses the same classes Core emits.
        $wrapper_class = 'wp-block-button';
        $link_class    = 'wp-block-button__link';
        if ( ! empty( $attrs['className'] ) ) {
            $link_class .= ' ' . sanitize_html_class( $attrs['className'] );
        }
        // Mark as icon-only for styling if you want to target it in CSS.
        $link_class .= ' has-only-icon';

        // Add icon class for styling
        $wrapper_class .= ' has-icon__' . sanitize_html_class( $icon );
        if ( $position_left ) {
            $wrapper_class .= ' has-icon-position__left';
        }
        if ( $is_only_icon ) {
            $wrapper_class .= ' has-icon-only';
        }

        $attrs_html = '';
        if ( $url ) {
            $attrs_html .= ' href="' .  $url  . '"';
        }
        if ( $rel ) {
            $attrs_html .= ' rel="' . esc_attr( $rel ) . '"';
        }
        if ( $target_attr ) {
            $attrs_html .= ' target="' . esc_attr( $target_attr ) . '"';
        }
        if ( $aria_label ) {
            // Strongly recommended for accessibility when there’s no visible text.
            $attrs_html .= ' aria-label="' . esc_attr( $aria_label ) . '"';
        }

        // Optional: wrap the SVG for easier positioning.
        $icon_html = sprintf(
            '<span class="button-icon" aria-hidden="true">%s</span>',
            $icon_svg
        );

        // Add CSS variable for icon height
        $wrapper_style = '';
        if ( $icon_height ) {
            $wrapper_style = ' style="height: ' . esc_attr( $icon_height ) . ';"';
        }

        return sprintf(
            '<div class="%1$s"%2$s><a class="%3$s"%4$s>%5$s</a></div>',
            esc_attr( $wrapper_class ),
            $wrapper_style,
            esc_attr( $link_class ),
            $attrs_html,
            $icon_html
        );
    }
    // Otherwise, inject the icon into the existing content (text + icon).
    // Simple insertion just inside the <a> tag, left or right position.
    if ( preg_match( '/<a\b[^>]*>/', $block_content, $m, PREG_OFFSET_CAPTURE ) ) {
        $a_open_tag      = $m[0][0];
        $insert_position = $m[0][1] + strlen( $a_open_tag );
        $icon_html       = '<span class="button-icon' . ( $position_left ? ' is-left' : ' is-right' ) . '" aria-hidden="true">' . $icon_svg . '</span>';

        if ( $position_left ) {
            // Icon before text.
            $block_content = substr( $block_content, 0, $insert_position ) . $icon_html . substr( $block_content, $insert_position );
        } else {
            // Icon after text: insert before closing </a>
            $block_content = preg_replace( '#</a>\s*</div>\s*$#', $icon_html . '</a></div>', $block_content, 1 );
        }

        // Add icon classes and CSS variable to the wrapper div
        $icon_classes = 'has-icon__' . sanitize_html_class( $icon );
        if ( $position_left ) {
            $icon_classes .= ' has-icon-position__left';
        }
        if ( $is_only_icon ) {
            $icon_classes .= ' has-icon-only';
        }

        // Add inline style for icon height
        $style_attr = '';
        if ( $icon_height ) {
            $style_attr = ' style="height: ' . esc_attr( $icon_height ) . ';"';
        }

        // Add classes to the wrapper div
        $block_content = preg_replace(
            '/(<div class="[^"]*wp-block-button[^"]*")/',
            '$1 ' . esc_attr( $icon_classes ) . '"' . $style_attr,
            $block_content,
            1
        );
    }

    return $block_content;
}
add_filter( 'render_block_core/button', 'enable_button_icons_render_block_button', 10, 2 );
/**
 * Render icons on the frontend.
 */


// Register REST API endpoint for fetching languages
function polylang_register_rest_routes() {
    register_rest_route('polylang/v1', '/languages', array(
        'methods' => 'GET',
        'callback' => 'polylang_get_languages',
    ));
}
add_action('rest_api_init', 'polylang_register_rest_routes');

function polylang_get_languages() {
    if (function_exists('pll_the_languages')) {
        $languages = pll_the_languages(array('raw' => 1));
        if (!empty($languages)) {
            return array_map(function($lang) {
                return array(
                    'slug' => $lang['slug'],
                    'name' => $lang['name'],
                    'url' => $lang['url'],
                    'flag' => $lang['flag'],
                    'current_language' => $lang['current_lang'],
                );
            }, $languages);
        }
    }
    return new WP_Error('no_languages', 'No languages found', array('status' => 404));
}
